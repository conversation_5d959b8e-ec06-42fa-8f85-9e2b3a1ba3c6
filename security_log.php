<?php
/**
 * Security Logging System for ABLA.LAT
 * Logs security events and potential attacks
 */

class SecurityLogger {
    private $log_file;
    private $max_log_size = 10485760; // 10MB
    
    public function __construct() {
        $this->log_file = __DIR__ . '/logs/security.log';
        $this->ensureLogDirectory();
    }
    
    private function ensureLogDirectory() {
        $log_dir = dirname($this->log_file);
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        // Create .htaccess to protect log directory
        $htaccess_file = $log_dir . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            file_put_contents($htaccess_file, "Deny from all\n");
        }
    }
    
    public function logEvent($event_type, $message, $severity = 'INFO', $additional_data = []) {
        $timestamp = date('Y-m-d H:i:s');
        $client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $request_uri = $_SERVER['REQUEST_URI'] ?? 'unknown';
        
        $log_entry = [
            'timestamp' => $timestamp,
            'event_type' => $event_type,
            'severity' => $severity,
            'message' => $message,
            'client_ip' => $client_ip,
            'user_agent' => $user_agent,
            'request_uri' => $request_uri,
            'additional_data' => $additional_data
        ];
        
        $log_line = json_encode($log_entry) . "\n";
        
        // Rotate log if too large
        if (file_exists($this->log_file) && filesize($this->log_file) > $this->max_log_size) {
            $this->rotateLog();
        }
        
        file_put_contents($this->log_file, $log_line, FILE_APPEND | LOCK_EX);
    }
    
    private function rotateLog() {
        $backup_file = $this->log_file . '.' . date('Y-m-d-H-i-s') . '.bak';
        rename($this->log_file, $backup_file);
        
        // Keep only last 5 backup files
        $log_dir = dirname($this->log_file);
        $backup_files = glob($log_dir . '/security.log.*.bak');
        if (count($backup_files) > 5) {
            usort($backup_files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // Remove oldest files
            for ($i = 0; $i < count($backup_files) - 5; $i++) {
                unlink($backup_files[$i]);
            }
        }
    }
    
    public function logLoginAttempt($username, $success, $additional_info = []) {
        $event_type = $success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED';
        $severity = $success ? 'INFO' : 'WARNING';
        $message = $success ? 
            "Successful login for user: {$username}" : 
            "Failed login attempt for user: {$username}";
        
        $this->logEvent($event_type, $message, $severity, array_merge([
            'username' => $username
        ], $additional_info));
    }
    
    public function logSuspiciousActivity($activity_type, $details) {
        $this->logEvent('SUSPICIOUS_ACTIVITY', $activity_type, 'WARNING', $details);
    }
    
    public function logDDoSAttempt($attack_type, $details) {
        $this->logEvent('DDOS_ATTEMPT', $attack_type, 'CRITICAL', $details);
    }
    
    public function logCaptchaFailure($details) {
        $this->logEvent('CAPTCHA_FAILED', 'CAPTCHA validation failed', 'WARNING', $details);
    }
    
    public function logHoneypotTrigger($details) {
        $this->logEvent('HONEYPOT_TRIGGERED', 'Bot detected via honeypot', 'WARNING', $details);
    }
    
    public function logRateLimitExceeded($limit_type, $details) {
        $this->logEvent('RATE_LIMIT_EXCEEDED', "Rate limit exceeded: {$limit_type}", 'WARNING', $details);
    }
}

// Global security logger instance
function getSecurityLogger() {
    static $logger = null;
    if ($logger === null) {
        $logger = new SecurityLogger();
    }
    return $logger;
}

// Helper functions
function logLoginAttempt($username, $success, $additional_info = []) {
    getSecurityLogger()->logLoginAttempt($username, $success, $additional_info);
}

function logSuspiciousActivity($activity_type, $details = []) {
    getSecurityLogger()->logSuspiciousActivity($activity_type, $details);
}

function logDDoSAttempt($attack_type, $details = []) {
    getSecurityLogger()->logDDoSAttempt($attack_type, $details);
}

function logCaptchaFailure($details = []) {
    getSecurityLogger()->logCaptchaFailure($details);
}

function logHoneypotTrigger($details = []) {
    getSecurityLogger()->logHoneypotTrigger($details);
}

function logRateLimitExceeded($limit_type, $details = []) {
    getSecurityLogger()->logRateLimitExceeded($limit_type, $details);
}
?>
