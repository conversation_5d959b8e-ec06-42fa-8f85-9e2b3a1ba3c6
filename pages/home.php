<?php
// Get hero content from database
$db = connectDB();
$hero = $db->query("SELECT * FROM content WHERE section = 'hero' LIMIT 1");

$hero_content = null;
if ($hero && $hero->num_rows > 0) {
    $hero_content = $hero->fetch_assoc();
}

// Get initiatives for the homepage
$initiatives = [];
$result = $db->query("SELECT * FROM initiatives ORDER BY id DESC");

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $initiatives[] = $row;
    }
}
?>

<!-- Hero Section -->
<section id="home" class="min-h-[80vh] md:min-h-[85vh] flex items-start md:items-center justify-center bg-black text-white pt-8 md:pt-12 pb-12 md:pb-16 px-4 relative overflow-hidden">
    <!-- Bitcoin Network Background -->
    <div class="bitcoin-network" id="bitcoin-network"></div>

    <!-- Bitcoin Particles Background -->
    <div class="bitcoin-particles" id="bitcoin-particles"></div>

    <div class="container mx-auto max-w-6xl relative z-10 mt-4 md:mt-0">
        <!-- Bitcoin Price Ticker - Moved to avoid text overlap -->
        <div class="absolute top-4 right-4 md:top-8 md:right-8 animate-floating-x z-20 hidden sm:block" style="animation-delay: 1.5s;">
            <div class="price-ticker" id="bitcoin-price">
                <i class="ph ph-currency-btc text-orange-500"></i>
                <span class="price-value" id="btc-price">$45,231.67</span>
                <span class="text-xs text-green-500" id="btc-change">****%</span>
            </div>
        </div>

        <!-- Mobile Price Ticker -->
        <div class="sm:hidden mb-4 flex justify-center animate-fadeIn" style="animation-delay: 1.5s;">
            <div class="price-ticker" id="bitcoin-price-mobile">
                <i class="ph ph-currency-btc text-orange-500"></i>
                <span class="price-value" id="btc-price-mobile">$45,231.67</span>
                <span class="text-xs text-green-500" id="btc-change-mobile">****%</span>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div class="animate-fadeIn relative z-20">
                <!-- Text background for better visibility -->
                <div class="absolute inset-0 bg-black/15 backdrop-blur-sm rounded-2xl -m-3 p-3 opacity-0 animate-fadeIn" style="animation-delay: 0.2s; animation-fill-mode: forwards;"></div>

                <div class="relative z-10">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold uppercase mb-4 md:mb-6 leading-tight">
                        <?php if ($hero_content && !empty($hero_content['title'])): ?>
                            <?php echo $hero_content['title']; ?>
                        <?php else: ?>
                            ASOCIACIÓN <span class="text-orange-500 animate-text-glow">BITCOIN</span> LATINOAMÉRICANA
                        <?php endif; ?>
                    </h1>

                    <p class="text-lg md:text-xl mb-6 md:mb-8 text-gray-200 animate-slideIn leading-relaxed" style="animation-delay: 0.3s;">
                        <?php if ($hero_content && !empty($hero_content['content'])): ?>
                            <?php echo $hero_content['content']; ?>
                        <?php else: ?>
                            Promoviendo la adopción de Bitcoin en Latinoamérica a través de educación, desarrollo y colaboración entre comunidades.
                        <?php endif; ?>
                    </p>
                </div>

                <div class="relative z-10">
                    <div class="flex flex-wrap gap-4 animate-slideIn" style="animation-delay: 0.6s;">
                        <a href="#descubre" class="btn btn-primary smooth-scroll">
                            <i class="ph ph-compass"></i> Descubre ABLA
                        </a>
                        <a href="#sumate" class="btn btn-secondary smooth-scroll">
                            <i class="ph ph-users"></i> Súmate
                        </a>
                    </div>
                </div>

                <!-- Lightning Network Animation -->
                <div class="mt-12 relative h-24 hidden md:block">
                    <div class="absolute left-0 top-0 animate-floating-xy" style="animation-delay: 0.2s;">
                        <div class="relative">
                            <i class="ph ph-currency-btc text-orange-500 text-3xl"></i>
                            <div class="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <div class="absolute left-24 top-8 animate-floating-xy" style="animation-delay: 0.7s;">
                        <div class="relative">
                            <i class="ph ph-lightning text-orange-500 text-3xl animate-lightning" style="--delay: 0.5"></i>
                            <div class="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <div class="absolute left-48 top-2 animate-floating-xy" style="animation-delay: 1.2s;">
                        <div class="relative">
                            <i class="ph ph-wallet text-orange-500 text-3xl"></i>
                            <div class="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <div class="absolute left-72 top-10 animate-floating-xy" style="animation-delay: 0.5s;">
                        <div class="relative">
                            <i class="ph ph-globe text-orange-500 text-3xl"></i>
                            <div class="absolute -top-1 -right-1 w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>

                    <!-- Lightning connections -->
                    <svg class="absolute top-0 left-0 w-full h-full" style="z-index: -1;">
                        <line id="line1" x1="12" y1="12" x2="96" y2="40" stroke="url(#lightning-gradient)" stroke-width="2" stroke-dasharray="5,5" />
                        <line id="line2" x1="96" y1="40" x2="192" y2="12" stroke="url(#lightning-gradient)" stroke-width="2" stroke-dasharray="5,5" />
                        <line id="line3" x1="192" y1="12" x2="288" y2="40" stroke="url(#lightning-gradient)" stroke-width="2" stroke-dasharray="5,5" />
                        <line id="line4" x1="12" y1="12" x2="288" y2="40" stroke="url(#lightning-gradient)" stroke-width="2" stroke-dasharray="5,5" stroke-opacity="0.3" />

                        <defs>
                            <linearGradient id="lightning-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" stop-color="#F7931A" stop-opacity="0.2" />
                                <stop offset="50%" stop-color="#F7931A" stop-opacity="1" />
                                <stop offset="100%" stop-color="#F7931A" stop-opacity="0.2" />
                            </linearGradient>
                        </defs>
                    </svg>
                </div>

                <!-- Bitcoin Stats -->
                <div class="relative z-10">
                    <div class="mt-8 grid grid-cols-3 gap-4 animate-slideIn" style="animation-delay: 0.9s;">
                        <div class="bg-gray-900/70 backdrop-blur-sm rounded-lg p-3 border border-orange-500/30 hover:border-orange-500/50 transition-colors">
                            <div class="text-xs text-gray-400">Hashrate</div>
                            <div class="font-mono text-sm flex items-center gap-1">
                                <i class="ph ph-hash text-orange-500 text-xs"></i>
                                <span class="text-white">512 EH/s</span>
                            </div>
                        </div>

                        <div class="bg-gray-900/70 backdrop-blur-sm rounded-lg p-3 border border-orange-500/30 hover:border-orange-500/50 transition-colors">
                            <div class="text-xs text-gray-400">Dificultad</div>
                            <div class="font-mono text-sm flex items-center gap-1">
                                <i class="ph ph-chart-line-up text-orange-500 text-xs"></i>
                                <span class="text-white">72.8T</span>
                            </div>
                        </div>

                        <div class="bg-gray-900/70 backdrop-blur-sm rounded-lg p-3 border border-orange-500/30 hover:border-orange-500/50 transition-colors">
                            <div class="text-xs text-gray-400">Bloque</div>
                            <div class="font-mono text-sm flex items-center gap-1">
                                <i class="ph ph-cube text-orange-500 text-xs"></i>
                                <span class="text-white">#823,456</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center lg:justify-end">
                <div class="relative">
                    <!-- 3D Bitcoin Coin -->
                    <div class="bitcoin-3d-container w-64 h-64 md:w-80 md:h-80">
                        <div class="bitcoin-3d w-full h-full">
                            <!-- Front face -->
                            <div class="bitcoin-face bitcoin-face-front flex items-center justify-center">
                                <i class="ph ph-bitcoin-circle text-white text-9xl"></i>
                            </div>

                            <!-- Back face -->
                            <div class="bitcoin-face bitcoin-face-back flex items-center justify-center">
                                <i class="ph ph-currency-btc text-white text-9xl"></i>
                            </div>

                            <!-- Edge -->
                            <div class="bitcoin-edge"></div>
                        </div>

                        <!-- Orbiting elements -->
                        <div class="absolute inset-0">
                            <!-- Orbit 1 -->
                            <div class="absolute top-1/2 left-1/2 w-0 h-0">
                                <div class="animate-orbit">
                                    <div class="relative -ml-3 -mt-3">
                                        <i class="ph ph-lightning text-orange-500 text-2xl animate-lightning" style="--delay: 0"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Orbit 2 -->
                            <div class="absolute top-1/2 left-1/2 w-0 h-0">
                                <div class="animate-orbit-reverse">
                                    <div class="relative -ml-3 -mt-3">
                                        <i class="ph ph-currency-btc text-orange-500 text-2xl"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Orbit 3 - Nodes -->
                            <div class="absolute top-1/2 left-1/2 w-0 h-0" style="transform: rotate(45deg);">
                                <div class="animate-orbit" style="animation-duration: 15s;">
                                    <div class="relative -ml-2 -mt-2">
                                        <div class="w-4 h-4 bg-orange-500 rounded-full opacity-70"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="absolute top-1/2 left-1/2 w-0 h-0" style="transform: rotate(135deg);">
                                <div class="animate-orbit" style="animation-duration: 20s;">
                                    <div class="relative -ml-1.5 -mt-1.5">
                                        <div class="w-3 h-3 bg-orange-500 rounded-full opacity-70"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="absolute top-1/2 left-1/2 w-0 h-0" style="transform: rotate(225deg);">
                                <div class="animate-orbit-reverse" style="animation-duration: 25s;">
                                    <div class="relative -ml-1 -mt-1">
                                        <div class="w-2 h-2 bg-orange-500 rounded-full opacity-70"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bitcoin Hashtag -->
                    <div class="absolute -bottom-4 -right-4 bg-white text-black rounded-full py-2 px-4 font-bold animate-floating-xy" style="animation-delay: 1s;">
                        #Bitcoin
                    </div>

                    <!-- Glowing effect -->
                    <div class="absolute inset-0 rounded-full bg-orange-500 filter blur-xl opacity-20 animate-bitcoin-pulse" style="animation-delay: 0.5s;"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add JavaScript for Bitcoin animations -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Bitcoin particles
    const particlesContainer = document.getElementById('bitcoin-particles');
    const particleCount = 20;

    // Create Bitcoin particles
    for (let i = 0; i < particleCount; i++) {
        createParticle();
    }

    function createParticle() {
        const particle = document.createElement('div');
        particle.classList.add('bitcoin-particle');

        // Random position, size and animation duration
        const size = Math.random() * 15 + 5;
        const left = Math.random() * 100;
        const delay = Math.random() * 5;
        const duration = Math.random() * 10 + 10;

        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.left = `${left}%`;
        particle.style.bottom = '-20px';
        particle.style.opacity = Math.random() * 0.3;
        particle.style.animation = `particleFloat ${duration}s linear infinite`;
        particle.style.animationDelay = `${delay}s`;

        // Use Bitcoin symbol for some particles
        if (Math.random() > 0.7) {
            particle.innerHTML = '<i class="ph ph-currency-btc" style="color: #F7931A; font-size: ' + size + 'px;"></i>';
            particle.style.background = 'transparent';
        }

        particlesContainer.appendChild(particle);
    }

    // Bitcoin network animation
    const networkContainer = document.getElementById('bitcoin-network');
    const nodeCount = 8;
    const nodes = [];

    // Create network nodes
    for (let i = 0; i < nodeCount; i++) {
        createNode(i);
    }

    function createNode(index) {
        const node = document.createElement('div');
        node.classList.add('network-node');

        // Position nodes in a grid-like pattern
        const row = Math.floor(index / 3);
        const col = index % 3;

        // Add some randomness to positions
        const xPos = 20 + col * 30 + (Math.random() * 10 - 5);
        const yPos = 20 + row * 30 + (Math.random() * 10 - 5);

        node.style.left = `${xPos}%`;
        node.style.top = `${yPos}%`;

        networkContainer.appendChild(node);
        nodes.push({
            element: node,
            x: xPos,
            y: yPos
        });
    }

    // Create connections between nodes
    for (let i = 0; i < nodes.length; i++) {
        for (let j = i + 1; j < nodes.length; j++) {
            // Only connect some nodes (not all-to-all)
            if (Math.random() > 0.7) {
                createConnection(nodes[i], nodes[j]);
            }
        }
    }

    function createConnection(nodeA, nodeB) {
        const line = document.createElement('div');
        line.classList.add('network-line');

        // Calculate line position and length
        const dx = nodeB.x - nodeA.x;
        const dy = nodeB.y - nodeA.y;
        const length = Math.sqrt(dx * dx + dy * dy);
        const angle = Math.atan2(dy, dx) * 180 / Math.PI;

        line.style.width = `${length}%`;
        line.style.left = `${nodeA.x}%`;
        line.style.top = `${nodeA.y}%`;
        line.style.transform = `rotate(${angle}deg)`;
        line.style.animationDelay = `${Math.random() * 5}s`;

        networkContainer.appendChild(line);
    }

    // Bitcoin price ticker animation
    const priceElement = document.getElementById('btc-price');
    const changeElement = document.getElementById('btc-change');
    const priceElementMobile = document.getElementById('btc-price-mobile');
    const changeElementMobile = document.getElementById('btc-change-mobile');

    // Simulate price updates
    function updatePrice() {
        const currentPrice = parseFloat(priceElement.textContent.replace('$', '').replace(',', ''));
        const change = (Math.random() - 0.45) * 100; // Slightly biased towards positive
        const newPrice = currentPrice + change;

        // Format price
        const formattedPrice = '$' + newPrice.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        // Update change percentage
        const changePercent = (change / currentPrice * 100).toFixed(2);
        const isPositive = changePercent >= 0;
        const changeText = (isPositive ? '+' : '') + changePercent + '%';
        const changeClass = 'text-xs ' + (isPositive ? 'text-green-500' : 'text-red-500');

        // Update desktop ticker
        if (priceElement) {
            priceElement.textContent = formattedPrice;
            changeElement.textContent = changeText;
            changeElement.className = changeClass;

            // Add animation class
            priceElement.classList.remove('ticker-up', 'ticker-down');
            void priceElement.offsetWidth; // Trigger reflow
            priceElement.classList.add(isPositive ? 'ticker-up' : 'ticker-down');
        }

        // Update mobile ticker
        if (priceElementMobile) {
            priceElementMobile.textContent = formattedPrice;
            changeElementMobile.textContent = changeText;
            changeElementMobile.className = changeClass;

            // Add animation class
            priceElementMobile.classList.remove('ticker-up', 'ticker-down');
            void priceElementMobile.offsetWidth; // Trigger reflow
            priceElementMobile.classList.add(isPositive ? 'ticker-up' : 'ticker-down');
        }

        // Schedule next update
        setTimeout(updatePrice, Math.random() * 5000 + 3000);
    }

    // Initial delay before starting price updates
    setTimeout(updatePrice, 3000);

    // Animate SVG lightning lines
    const lines = document.querySelectorAll('#line1, #line2, #line3, #line4');

    lines.forEach((line, index) => {
        // Create animation for each line
        const animate = document.createElementNS('http://www.w3.org/2000/svg', 'animate');
        animate.setAttribute('attributeName', 'stroke-dashoffset');
        animate.setAttribute('from', '10');
        animate.setAttribute('to', '0');
        animate.setAttribute('dur', '1.5s');
        animate.setAttribute('repeatCount', 'indefinite');

        line.appendChild(animate);

        // Add some delay between animations
        animate.beginElement();
        setTimeout(() => {
            animate.beginElement();
        }, index * 400);
    });
});
</script>

<script>
// Initiatives Carousel
const carousel = document.querySelector('.carousel-container');
const prevBtn = document.querySelector('.carousel-prev');
const nextBtn = document.querySelector('.carousel-next');
const cards = document.querySelectorAll('.card');
let currentIndex = 0;

// Initialize Hammer.js for touch support
if (typeof Hammer !== 'undefined') {
    const hammer = new Hammer(carousel);
    hammer.on('swipeleft', () => nextCard());
    hammer.on('swiperight', () => prevCard());
}

function updateCarousel() {
    const cardWidth = cards[0].offsetWidth + 16; // width + margin
    carousel.scrollTo({
        left: currentIndex * cardWidth,
        behavior: 'smooth'
    });
}

function nextCard() {
    if (currentIndex < cards.length - 1) {
        currentIndex++;
        updateCarousel();
    }
}

function prevCard() {
    if (currentIndex > 0) {
        currentIndex--;
        updateCarousel();
    }
}

prevBtn.addEventListener('click', prevCard);
nextBtn.addEventListener('click', nextCard);

// Auto-scroll every 5 seconds
let autoScroll = setInterval(nextCard, 5000);

// Pause auto-scroll on hover/interaction
carousel.addEventListener('mouseenter', () => clearInterval(autoScroll));
carousel.addEventListener('mouseleave', () => {
    autoScroll = setInterval(nextCard, 5000);
});

// Infinite scroll (loop back to start)
carousel.addEventListener('scrollend', () => {
    if (currentIndex >= cards.length - 3) {
        setTimeout(() => {
            currentIndex = 0;
            updateCarousel();
        }, 3000);
    }
});
</script>

<!-- Initiatives Section -->
<section id="descubre" class="py-16 px-4" style="background-color: var(--card-bg);">
    <div class="container mx-auto max-w-6xl">
        <h2 class="text-3xl font-bold text-center mb-12">Iniciativas <span class="text-orange-500">Bitcoin</span> en Latinoamérica</h2>

        <div class="initiatives-carousel relative">
            <div class="carousel-container flex overflow-x-auto snap-x snap-mandatory scroll-smooth pb-4 -mx-4 px-4" style="scrollbar-width: none;">
            <?php if (!empty($initiatives)): ?>
                <?php foreach ($initiatives as $initiative): ?>
                <div class="card flex-none snap-start w-[85vw] md:w-[40vw] lg:w-[30vw] mx-2">
                    <!-- Avatar and Header -->
                    <div class="flex items-center mb-4">
                        <?php
                        require_once 'includes/avatar_fetcher.php';
                        $avatar_url = getAvatarForDisplay($initiative);
                        ?>
                        <img src="<?php echo htmlspecialchars($avatar_url); ?>"
                             alt="<?php echo htmlspecialchars($initiative['name']); ?>"
                             class="w-12 h-12 rounded-full object-cover border-2 border-orange-500/30 mr-4"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiNGNzkzMUEiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTMuMSAyIDE0IDIuOSAxNCA0QzE0IDUuMSAxMy4xIDYgMTIgNkMxMC45IDYgMTAgNS4xIDEwIDRDMTAgMi45IDEwLjkgMiAxMiAyWk0yMSAxOVYyMEgzVjE5QzMgMTYuMzMgOC42NyAxNSAxMiAxNUMxNS4zMyAxNSAyMSAxNi4zMyAyMSAxOVpNMTIgMTNDOS43OSAxMyA4IDExLjIxIDggOUM4IDYuNzkgOS43OSA1IDEyIDVDMTQuMjEgNSAxNiA2Ljc5IDE2IDlDMTYgMTEuMjEgMTQuMjEgMTMgMTIgMTNaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+'">

                        <div>
                            <h3 class="text-xl font-bold mb-1"><?php echo htmlspecialchars($initiative['name']); ?></h3>
                            <div class="flex items-center gap-3 text-sm">
                                <?php if (!empty($initiative['instagram_user'])): ?>
                                    <span class="text-pink-500 flex items-center gap-1">
                                        <i class="ph ph-instagram-logo"></i>
                                        <?php echo htmlspecialchars($initiative['instagram_user']); ?>
                                    </span>
                                <?php endif; ?>

                                <?php if (!empty($initiative['twitter_user'])): ?>
                                    <span class="text-blue-400 flex items-center gap-1">
                                        <i class="ph ph-x-logo"></i>
                                        <?php echo htmlspecialchars($initiative['twitter_user']); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="text-gray-300 mb-6 initiative-description"><?php echo $initiative['description']; ?></div>

                    <!-- Social Links -->
                    <div class="flex gap-2">
                        <?php if (!empty($initiative['instagram_link'])): ?>
                            <a href="<?php echo htmlspecialchars($initiative['instagram_link']); ?>" target="_blank" class="btn btn-secondary btn-sm">
                                <i class="ph ph-instagram-logo"></i> Instagram
                            </a>
                        <?php endif; ?>

                        <?php if (!empty($initiative['twitter_link'])): ?>
                            <a href="<?php echo htmlspecialchars($initiative['twitter_link']); ?>" target="_blank" class="btn btn-secondary btn-sm">
                                <i class="ph ph-x-logo"></i> X
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Carousel Navigation -->
            <div class="flex justify-center mt-8 gap-4">
                <button class="carousel-prev bg-orange-500 hover:bg-orange-600 text-white w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="ph ph-caret-left"></i>
                </button>
                <button class="carousel-next bg-orange-500 hover:bg-orange-600 text-white w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="ph ph-caret-right"></i>
                </button>
            </div>
            
            <?php else: ?>
                <!-- Placeholder initiatives if none in database -->
                <div class="card flex-none snap-start w-[85vw] md:w-[40vw] lg:w-[30vw] mx-2">
                    <h3 class="text-xl font-bold mb-2">Bitcoin Beach</h3>
                    <p class="text-orange-500 mb-4">@bitcoinbeach</p>
                    <p class="text-gray-300 mb-6">Proyecto pionero de adopción de Bitcoin en El Zonte, El Salvador, que inspiró la ley Bitcoin en el país.</p>
                    <a href="https://www.instagram.com/bitcoinbeach/" target="_blank" class="btn btn-secondary btn-sm">
                        <i class="ph ph-instagram-logo"></i> Ver perfil
                    </a>
                </div>

                <div class="card flex-none snap-start w-[85vw] md:w-[40vw] lg:w-[30vw] mx-2">
                    <h3 class="text-xl font-bold mb-2">Mi Primer Bitcoin</h3>
                    <p class="text-orange-500 mb-4">@myfirstbitcoin.io</p>
                    <p class="text-gray-300 mb-6">Educación Bitcoin gratuita para todos. Pioneros en educación Bitcoin en El Salvador.</p>
                    <a href="https://www.instagram.com/myfirstbitcoin.io/" target="_blank" class="btn btn-secondary btn-sm">
                        <i class="ph ph-instagram-logo"></i> Ver perfil
                    </a>
                </div>

                <div class="card flex-none snap-start w-[85vw] md:w-[40vw] lg:w-[30vw] mx-2">
                    <h3 class="text-xl font-bold mb-2">Bitcoin Argentina</h3>
                    <p class="text-orange-500 mb-4">@bitcoinargentina</p>
                    <p class="text-gray-300 mb-6">ONG que promueve y desarrolla el ecosistema Bitcoin y blockchain en Argentina desde 2013.</p>
                    <a href="https://www.instagram.com/bitcoinargentina/" target="_blank" class="btn btn-secondary btn-sm">
                        <i class="ph ph-instagram-logo"></i> Ver perfil
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <div class="text-center mt-12">
            <a href="#contacto" class="btn btn-primary smooth-scroll">
                <i class="ph ph-plus-circle"></i> Contáctanos para más iniciativas
            </a>
        </div>
    </div>
</section>

<!-- About Us Section -->
<section id="nosotros" class="py-16 px-4" style="background-color: var(--dark-bg);">
    <div class="container mx-auto max-w-6xl">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h2 class="text-3xl font-bold mb-6">Sobre <span class="text-orange-500">Nosotros</span></h2>
                <p class="text-lg mb-6">La Asociación Bitcoin Latinoaméricana (ABLA) es una organización sin fines de lucro dedicada a promover la adopción de Bitcoin en toda América Latina.</p>

                <div class="space-y-4 mb-8">
                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-graduation-cap text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Educación</h3>
                            <p>Desarrollamos programas educativos para difundir el conocimiento sobre Bitcoin y su tecnología subyacente.</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-handshake text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Colaboración</h3>
                            <p>Conectamos iniciativas Bitcoin en toda la región para fortalecer el ecosistema latinoamericano.</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-lightbulb text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Innovación</h3>
                            <p>Apoyamos proyectos innovadores que utilizan Bitcoin para resolver problemas reales en nuestras comunidades.</p>
                        </div>
                    </div>
                </div>

                <a href="#eventos" class="btn btn-secondary smooth-scroll">
                    <i class="ph ph-calendar"></i> Nuestros Eventos
                </a>
            </div>

            <div class="flex justify-center">
                <div class="relative">
                    <div class="w-full max-w-md h-80 bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg overflow-hidden">
                        <div class="absolute inset-0 opacity-30">
                            <div class="absolute top-0 left-0 w-full h-full bg-repeat" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjZjc5MzFhIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0wIDBoNDB2NDBoLTQweiIgZmlsbD0ibm9uZSIvPjxwYXRoIGQ9Ik0xMCAxMGgyMHYyMGgtMjB6IiBmaWxsLW9wYWNpdHk9Ii4yIi8+PC9nPjwvc3ZnPg==');"></div>
                        </div>

                        <div class="relative z-10 flex flex-col items-center justify-center h-full p-6 text-center">
                            <div class="w-20 h-20 bg-orange-500 rounded-full flex items-center justify-center mb-6">
                                <i class="ph ph-bitcoin-circle text-white text-4xl"></i>
                            </div>

                            <h3 class="text-2xl font-bold mb-2">Misión</h3>
                            <p class="text-gray-300">Impulsar la adopción de Bitcoin en Latinoamérica para promover la inclusión financiera, la soberanía individual y el desarrollo económico.</p>
                        </div>
                    </div>

                    <!-- Decorative elements -->
                    <div class="absolute -top-4 -right-4 w-12 h-12 bg-orange-500 rounded-lg rotate-12"></div>
                    <div class="absolute -bottom-4 -left-4 w-8 h-8 bg-orange-500 rounded-full"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Events Section Placeholder -->
<section id="eventos" class="py-16 px-4 bg-gray-900">
    <div class="container mx-auto max-w-6xl">
        <h2 class="text-3xl font-bold text-center mb-12">Próximos <span class="text-orange-500">Eventos</span></h2>

        <div class="initiatives-carousel relative">
            <div class="carousel-container flex overflow-x-auto snap-x snap-mandatory scroll-smooth pb-4 -mx-4 px-4" style="scrollbar-width: none;">
            <div class="card">
                <div class="mb-4 relative">
                    <div class="bg-gray-800 h-48 rounded-lg flex items-center justify-center">
                        <i class="ph ph-calendar-plus text-orange-500 text-6xl"></i>
                    </div>
                    <div class="absolute top-4 right-4 bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded">
                        PRÓXIMAMENTE
                    </div>
                </div>
                <h3 class="text-xl font-bold mb-2">Bitcoin Meetup LATAM</h3>
                <p class="text-gray-400 mb-2 flex items-center gap-2">
                    <i class="ph ph-calendar"></i> Próximamente
                </p>
                <p class="text-gray-400 mb-4 flex items-center gap-2">
                    <i class="ph ph-map-pin"></i> Virtual
                </p>
                <p class="text-gray-300 mb-6">Encuentro virtual para conectar a entusiastas de Bitcoin en toda Latinoamérica. Presentaciones, networking y más.</p>
                <a href="#" class="btn btn-secondary btn-sm">
                    <i class="ph ph-bell-simple"></i> Recibir notificación
                </a>
            </div>

            <div class="card">
                <div class="mb-4 relative">
                    <div class="bg-gray-800 h-48 rounded-lg flex items-center justify-center">
                        <i class="ph ph-chalkboard-teacher text-orange-500 text-6xl"></i>
                    </div>
                    <div class="absolute top-4 right-4 bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded">
                        PRÓXIMAMENTE
                    </div>
                </div>
                <h3 class="text-xl font-bold mb-2">Taller: Bitcoin para Principiantes</h3>
                <p class="text-gray-400 mb-2 flex items-center gap-2">
                    <i class="ph ph-calendar"></i> Próximamente
                </p>
                <p class="text-gray-400 mb-4 flex items-center gap-2">
                    <i class="ph ph-map-pin"></i> Virtual
                </p>
                <p class="text-gray-300 mb-6">Aprende los fundamentos de Bitcoin, cómo configurar una wallet y realizar transacciones de forma segura.</p>
                <a href="#" class="btn btn-secondary btn-sm">
                    <i class="ph ph-bell-simple"></i> Recibir notificación
                </a>
            </div>

            <div class="card">
                <div class="mb-4 relative">
                    <div class="bg-gray-800 h-48 rounded-lg flex items-center justify-center">
                        <i class="ph ph-lightning text-orange-500 text-6xl"></i>
                    </div>
                    <div class="absolute top-4 right-4 bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded">
                        PRÓXIMAMENTE
                    </div>
                </div>
                <h3 class="text-xl font-bold mb-2">Lightning Network Hackathon</h3>
                <p class="text-gray-400 mb-2 flex items-center gap-2">
                    <i class="ph ph-calendar"></i> Próximamente
                </p>
                <p class="text-gray-400 mb-4 flex items-center gap-2">
                    <i class="ph ph-map-pin"></i> Virtual
                </p>
                <p class="text-gray-300 mb-6">Desarrolla aplicaciones innovadoras utilizando la Lightning Network. Premios para los mejores proyectos.</p>
                <a href="#" class="btn btn-secondary btn-sm">
                    <i class="ph ph-bell-simple"></i> Recibir notificación
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section id="sumate" class="py-16 px-4 bg-black">
    <div class="container mx-auto max-w-4xl text-center">
        <h2 class="text-3xl font-bold mb-6">¿Quieres formar parte de ABLA?</h2>
        <p class="text-xl mb-8">Únete a nuestra comunidad y contribuye al crecimiento de Bitcoin en América Latina.</p>
        <a href="#contacto" class="btn btn-primary smooth-scroll">
            <i class="ph ph-users-three"></i> Súmate a ABLA
        </a>
    </div>
</section>

<!-- Contact Form Section -->
<section id="contacto" class="py-16 px-4" style="background-color: var(--card-bg);">
    <div class="container mx-auto max-w-6xl">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div>
                <h2 class="text-3xl font-bold mb-6">Contacta con <span class="text-orange-500">Nosotros</span></h2>
                <p class="text-lg mb-8">¿Tienes preguntas o quieres formar parte de ABLA? Completa el formulario y nos pondremos en contacto contigo lo antes posible.</p>

                <div class="space-y-6">
                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-envelope text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Email</h3>
                            <p class="text-gray-300"><EMAIL></p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-map-pin text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Ubicación</h3>
                            <p class="text-gray-300">Latinoamérica</p>
                        </div>
                    </div>

                    <div class="flex items-start gap-4">
                        <div class="bg-orange-500 rounded-full p-2 mt-1">
                            <i class="ph ph-globe text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">Redes Sociales</h3>
                            <div class="flex gap-4 mt-2">
                                <a href="#" class="text-gray-400 hover:text-orange-500 transition-colors">
                                    <i class="ph ph-twitter-logo text-2xl"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-orange-500 transition-colors">
                                    <i class="ph ph-instagram-logo text-2xl"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-orange-500 transition-colors">
                                    <i class="ph ph-telegram-logo text-2xl"></i>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-orange-500 transition-colors">
                                    <i class="ph ph-github-logo text-2xl"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <form class="space-y-6" id="contactForm" action="/process_form.php" method="post">
                    <!-- Honeypot field to catch bots -->
                    <div style="display:none">
                        <label for="website">Website (leave this empty)</label>
                        <input type="text" id="website" name="website" autocomplete="off">
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Nombre</label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                placeholder="Tu nombre"
                                required
                            >
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                placeholder="<EMAIL>"
                                required
                            >
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="instagram" class="block text-sm font-medium text-gray-300 mb-1">
                                <i class="ph ph-instagram-logo mr-1"></i> Instagram <span class="text-xs text-gray-400">(opcional)</span>
                            </label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">@</span>
                                <input
                                    type="text"
                                    id="instagram"
                                    name="instagram"
                                    class="w-full p-3 pl-8 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                    placeholder="usuario_instagram"
                                >
                            </div>
                        </div>

                        <div>
                            <label for="twitter" class="block text-sm font-medium text-gray-300 mb-1">
                                <i class="ph ph-twitter-logo mr-1"></i> X / Twitter <span class="text-xs text-gray-400">(opcional)</span>
                            </label>
                            <div class="relative">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">@</span>
                                <input
                                    type="text"
                                    id="twitter"
                                    name="twitter"
                                    class="w-full p-3 pl-8 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                    placeholder="usuario_twitter"
                                >
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-300 mb-1">Asunto</label>
                        <input
                            type="text"
                            id="subject"
                            name="subject"
                            class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                            placeholder="Asunto de tu mensaje"
                            required
                        >
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-300 mb-1">Mensaje</label>
                        <textarea
                            id="message"
                            name="message"
                            rows="5"
                            class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                            placeholder="Tu mensaje"
                            required
                        ></textarea>
                    </div>

                    <!-- Simple Captcha -->
                    <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                        <label class="block text-sm font-medium text-gray-300 mb-3">Verificación de seguridad</label>

                        <div class="flex items-center gap-4">
                            <div id="captchaDisplay" class="bg-gray-900 px-4 py-2 rounded font-mono text-xl tracking-widest text-orange-500 select-none"></div>
                            <button type="button" id="refreshCaptcha" class="p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors">
                                <i class="ph ph-arrows-clockwise"></i>
                            </button>
                        </div>

                        <div class="mt-3">
                            <input
                                type="text"
                                id="captchaInput"
                                name="captcha"
                                class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500"
                                placeholder="Ingresa el código que ves arriba"
                                required
                            >
                        </div>
                    </div>

                    <div id="formMessage" class="hidden p-4 rounded-lg"></div>

                    <div>
                        <button type="submit" class="btn btn-primary w-full">
                            <i class="ph ph-paper-plane-right"></i> Enviar Mensaje
                        </button>
                    </div>
                </form>

                <!-- Captcha and Form Submission Script -->
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Initialize captcha on page load
                        const captchaDisplay = document.getElementById('captchaDisplay');

                        // Function to fetch a new captcha from the server
                        function fetchNewCaptcha() {
                            fetch('/get_captcha.php')
                                .then(response => response.json())
                                .then(data => {
                                    if (data.captcha) {
                                        captchaDisplay.textContent = data.captcha;
                                    }
                                })
                                .catch(error => {
                                    console.error('Error fetching captcha:', error);
                                    // Fallback to client-side captcha if server request fails
                                    captchaDisplay.textContent = generateClientCaptcha();
                                });
                        }

                        // Generate a client-side captcha as fallback
                        function generateClientCaptcha() {
                            const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                            let captcha = '';
                            for (let i = 0; i < 6; i++) {
                                const randomIndex = Math.floor(Math.random() * chars.length);
                                captcha += chars[randomIndex];
                            }
                            return captcha;
                        }

                        // Initial captcha load
                        fetchNewCaptcha();

                        // Refresh captcha button
                        const refreshButton = document.getElementById('refreshCaptcha');
                        refreshButton.addEventListener('click', function() {
                            fetchNewCaptcha();
                        });

                        // Form submission
                        const contactForm = document.getElementById('contactForm');
                        const formMessage = document.getElementById('formMessage');
                        const submitButton = contactForm.querySelector('button[type="submit"]');

                        contactForm.addEventListener('submit', function(e) {
                            e.preventDefault();

                            // Disable submit button and show loading state
                            submitButton.disabled = true;
                            submitButton.innerHTML = '<i class="ph ph-spinner ph-spin"></i> Enviando...';

                            // Get form data
                            const formData = new FormData(contactForm);

                            // Send form data to server
                            fetch('/process_form.php', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                // Display response message
                                formMessage.textContent = data.message;
                                formMessage.className = data.success
                                    ? 'p-4 rounded-lg bg-green-900 text-white'
                                    : 'p-4 rounded-lg bg-red-900 text-white';
                                formMessage.style.display = 'block';

                                // If successful, reset form
                                if (data.success) {
                                    contactForm.reset();
                                }

                                // Update captcha if provided
                                if (data.new_captcha) {
                                    captchaDisplay.textContent = data.new_captcha;
                                } else {
                                    // Otherwise fetch a new one
                                    fetchNewCaptcha();
                                }

                                // Scroll to message
                                formMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            })
                            .catch(error => {
                                console.error('Error submitting form:', error);
                                formMessage.textContent = 'Ha ocurrido un error al enviar el formulario. Por favor, inténtalo de nuevo más tarde.';
                                formMessage.className = 'p-4 rounded-lg bg-red-900 text-white';
                                formMessage.style.display = 'block';

                                // Fetch new captcha
                                fetchNewCaptcha();
                            })
                            .finally(() => {
                                // Re-enable submit button
                                submitButton.disabled = false;
                                submitButton.innerHTML = '<i class="ph ph-paper-plane-right"></i> Enviar Mensaje';
                            });
                        });
                    });
                 </script>
             </div>
         </div>
    </div>
</section>