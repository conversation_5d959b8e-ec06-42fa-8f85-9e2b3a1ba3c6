<?php
// Check if logged in
if(!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: https://abla.lat/?route=admin');
    exit;
}

// Database connection
$db = connectDB();
if (is_array($db)) {
    // Connection error
    $error = "Error de conexión a la base de datos: " . $db['error'];
    include 'admin/db_init_error.php';
    exit;
}

// Process export
$message = '';
$success = false;
$export_data = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['export_db'])) {
        // Get tables to export
        $tables = [];
        if (isset($_POST['export_tables']) && is_array($_POST['export_tables'])) {
            $tables = $_POST['export_tables'];
        }
        
        if (empty($tables)) {
            $message = "Por favor, selecciona al menos una tabla para exportar.";
        } else {
            // Export data from selected tables
            $export_data = [];
            
            foreach ($tables as $table) {
                // Sanitize table name to prevent SQL injection
                $table = preg_replace('/[^a-zA-Z0-9_]/', '', $table);
                
                // Get table structure
                $result = $db->query("SHOW CREATE TABLE `$table`");
                if ($result && $row = $result->fetch_assoc()) {
                    $export_data[$table]['structure'] = $row['Create Table'];
                }
                
                // Get table data
                $result = $db->query("SELECT * FROM `$table`");
                if ($result) {
                    $rows = [];
                    while ($row = $result->fetch_assoc()) {
                        $rows[] = $row;
                    }
                    $export_data[$table]['data'] = $rows;
                    $export_data[$table]['count'] = count($rows);
                }
            }
            
            if (!empty($export_data)) {
                $success = true;
                $message = "Datos preparados para exportación. Selecciona el método de exportación.";
            } else {
                $message = "No se encontraron datos para exportar.";
            }
        }
    } elseif (isset($_POST['export_to_remote'])) {
        // Remote database export
        $remote_host = $_POST['remote_host'] ?? '';
        $remote_user = $_POST['remote_user'] ?? '';
        $remote_password = $_POST['remote_password'] ?? '';
        $remote_database = $_POST['remote_database'] ?? '';
        $export_data_json = $_POST['export_data'] ?? '';
        
        if (empty($remote_host) || empty($remote_user) || empty($remote_database)) {
            $message = "Por favor, completa todos los campos obligatorios.";
        } else {
            // Decode the export data
            $export_data = json_decode($export_data_json, true);
            
            if (empty($export_data)) {
                $message = "No hay datos para exportar.";
            } else {
                // Connect to remote database
                try {
                    $remote_db = new mysqli($remote_host, $remote_user, $remote_password, $remote_database);
                    
                    if ($remote_db->connect_error) {
                        throw new Exception("Error de conexión: " . $remote_db->connect_error);
                    }
                    
                    // Start transaction
                    $remote_db->begin_transaction();
                    
                    try {
                        // Process each table
                        foreach ($export_data as $table => $table_data) {
                            // Check if table exists
                            $result = $remote_db->query("SHOW TABLES LIKE '$table'");
                            $table_exists = $result && $result->num_rows > 0;
                            
                            // Create table if it doesn't exist
                            if (!$table_exists && isset($table_data['structure'])) {
                                $remote_db->query($table_data['structure']);
                            }
                            
                            // Insert data
                            if (isset($table_data['data']) && !empty($table_data['data'])) {
                                // Truncate table if requested
                                if (isset($_POST['truncate_tables']) && $_POST['truncate_tables'] === 'yes') {
                                    $remote_db->query("TRUNCATE TABLE `$table`");
                                }
                                
                                // Insert each row
                                foreach ($table_data['data'] as $row) {
                                    $columns = array_keys($row);
                                    $values = array_values($row);
                                    
                                    // Prepare placeholders and escape values
                                    $placeholders = array_fill(0, count($values), '?');
                                    $types = str_repeat('s', count($values)); // Assume all are strings for simplicity
                                    
                                    // Prepare and execute statement
                                    $stmt = $remote_db->prepare("INSERT INTO `$table` (`" . implode("`, `", $columns) . "`) VALUES (" . implode(", ", $placeholders) . ")");
                                    
                                    if ($stmt) {
                                        $stmt->bind_param($types, ...$values);
                                        $stmt->execute();
                                        $stmt->close();
                                    }
                                }
                            }
                        }
                        
                        // Commit transaction
                        $remote_db->commit();
                        $success = true;
                        $message = "Datos exportados correctamente a la base de datos remota.";
                        
                    } catch (Exception $e) {
                        // Rollback transaction on error
                        $remote_db->rollback();
                        throw $e;
                    }
                    
                    $remote_db->close();
                    
                } catch (Exception $e) {
                    $message = "Error al exportar datos: " . $e->getMessage();
                }
            }
        }
    } elseif (isset($_POST['download_sql'])) {
        // Download SQL export
        $export_data_json = $_POST['export_data'] ?? '';
        $export_data = json_decode($export_data_json, true);
        
        if (!empty($export_data)) {
            // Generate SQL file content
            $sql_content = "-- ABLA Database Export\n";
            $sql_content .= "-- Generated: " . date('Y-m-d H:i:s') . "\n\n";
            
            foreach ($export_data as $table => $table_data) {
                $sql_content .= "-- Table structure for table `$table`\n";
                $sql_content .= "DROP TABLE IF EXISTS `$table`;\n";
                $sql_content .= $table_data['structure'] . ";\n\n";
                
                if (isset($table_data['data']) && !empty($table_data['data'])) {
                    $sql_content .= "-- Data for table `$table`\n";
                    $sql_content .= "TRUNCATE TABLE `$table`;\n";
                    
                    foreach ($table_data['data'] as $row) {
                        $columns = array_keys($row);
                        $values = array_map(function($value) use ($db) {
                            if ($value === null) {
                                return 'NULL';
                            } else {
                                return "'" . $db->real_escape_string($value) . "'";
                            }
                        }, array_values($row));
                        
                        $sql_content .= "INSERT INTO `$table` (`" . implode("`, `", $columns) . "`) VALUES (" . implode(", ", $values) . ");\n";
                    }
                    
                    $sql_content .= "\n";
                }
            }
            
            // Set headers for download
            header('Content-Type: application/sql');
            header('Content-Disposition: attachment; filename="abla_export_' . date('Y-m-d') . '.sql"');
            header('Content-Length: ' . strlen($sql_content));
            
            // Output SQL content
            echo $sql_content;
            exit;
        } else {
            $message = "No hay datos para exportar.";
        }
    }
}

// Get database information
$db_info = [];
$db_size = 0;

// Get database name
$db_name = '';
$result = $db->query("SELECT DATABASE()");
if ($result && $row = $result->fetch_row()) {
    $db_name = $row[0];
    $db_info['name'] = $db_name;
}

// Get tables
$tables = [];
$result = $db->query("SHOW TABLES");
if ($result) {
    while ($row = $result->fetch_row()) {
        $table_name = $row[0];
        $tables[] = $table_name;
        
        // Get table info
        $table_info = [];
        
        // Get row count
        $count_result = $db->query("SELECT COUNT(*) FROM `$table_name`");
        if ($count_result && $count_row = $count_result->fetch_row()) {
            $table_info['rows'] = $count_row[0];
        }
        
        // Get table size
        $size_result = $db->query("SELECT 
            data_length + index_length AS size,
            table_rows
            FROM information_schema.TABLES 
            WHERE table_schema = '$db_name' 
            AND table_name = '$table_name'");
        
        if ($size_result && $size_row = $size_result->fetch_assoc()) {
            $table_info['size'] = $size_row['size'];
            $db_size += $size_row['size'];
        }
        
        $db_info['tables'][$table_name] = $table_info;
    }
}

// Format database size
$db_info['size'] = $db_size;
$db_info['size_formatted'] = formatSize($db_size);

// Function to format size
function formatSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($bytes >= 1024 && $i < count($units) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}

// Get MySQL version
$result = $db->query("SELECT VERSION()");
if ($result && $row = $result->fetch_row()) {
    $db_info['mysql_version'] = $row[0];
}

// Get server information
$db_info['server_info'] = $db->server_info;
$db_info['host_info'] = $db->host_info;
?>

<div class="bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-white">Exportación de Base de Datos</h1>
            <a href="/?route=admin" class="btn btn-secondary">
                <i class="ph ph-arrow-left"></i> Volver al Dashboard
            </a>
        </div>
        
        <?php if (!empty($message)): ?>
        <div class="alert <?php echo $success ? 'alert-success' : 'alert-error'; ?> mb-8">
            <div class="alert-icon">
                <i class="ph <?php echo $success ? 'ph-check-circle' : 'ph-warning-circle'; ?>"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title"><?php echo $success ? 'Éxito' : 'Error'; ?></div>
                <div class="alert-message"><?php echo $message; ?></div>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Database Info Card -->
            <div class="lg:col-span-1">
                <div class="card mb-8">
                    <div class="card-header">
                        <h2 class="text-xl font-bold text-white flex items-center gap-2">
                            <i class="ph ph-database text-orange-500"></i> Información de la Base de Datos
                        </h2>
                    </div>
                    
                    <div class="p-6">
                        <div class="space-y-4">
                            <div>
                                <h3 class="text-sm font-medium text-gray-400">Nombre de la Base de Datos</h3>
                                <p class="text-white"><?php echo htmlspecialchars($db_info['name'] ?? 'N/A'); ?></p>
                            </div>
                            
                            <div>
                                <h3 class="text-sm font-medium text-gray-400">Tamaño Total</h3>
                                <p class="text-white"><?php echo htmlspecialchars($db_info['size_formatted'] ?? 'N/A'); ?></p>
                            </div>
                            
                            <div>
                                <h3 class="text-sm font-medium text-gray-400">Número de Tablas</h3>
                                <p class="text-white"><?php echo count($tables); ?></p>
                            </div>
                            
                            <div>
                                <h3 class="text-sm font-medium text-gray-400">Versión de MySQL</h3>
                                <p class="text-white"><?php echo htmlspecialchars($db_info['mysql_version'] ?? 'N/A'); ?></p>
                            </div>
                            
                            <div>
                                <h3 class="text-sm font-medium text-gray-400">Información del Servidor</h3>
                                <p class="text-white"><?php echo htmlspecialchars($db_info['server_info'] ?? 'N/A'); ?></p>
                            </div>
                            
                            <div>
                                <h3 class="text-sm font-medium text-gray-400">Conexión</h3>
                                <p class="text-white"><?php echo htmlspecialchars($db_info['host_info'] ?? 'N/A'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Table List Card -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-xl font-bold text-white flex items-center gap-2">
                            <i class="ph ph-table text-orange-500"></i> Tablas
                        </h2>
                    </div>
                    
                    <div class="p-6">
                        <div class="space-y-2">
                            <?php foreach ($tables as $table): ?>
                            <div class="flex justify-between items-center p-3 bg-gray-800 rounded-lg">
                                <div>
                                    <p class="text-white font-medium"><?php echo htmlspecialchars($table); ?></p>
                                    <p class="text-xs text-gray-400">
                                        <?php echo number_format($db_info['tables'][$table]['rows'] ?? 0); ?> filas
                                        (<?php echo formatSize($db_info['tables'][$table]['size'] ?? 0); ?>)
                                    </p>
                                </div>
                                <div>
                                    <a href="/?route=admin&page=db_export&action=view_table&table=<?php echo urlencode($table); ?>" class="text-orange-500 hover:text-orange-400">
                                        <i class="ph ph-eye"></i>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                            
                            <?php if (empty($tables)): ?>
                            <div class="p-4 bg-gray-800 rounded-lg text-center">
                                <p class="text-gray-400">No hay tablas disponibles.</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Export Options -->
            <div class="lg:col-span-2">
                <?php if (empty($export_data)): ?>
                <!-- Export Selection Form -->
                <div class="card mb-8">
                    <div class="card-header">
                        <h2 class="text-xl font-bold text-white flex items-center gap-2">
                            <i class="ph ph-database-export text-orange-500"></i> Exportar Base de Datos
                        </h2>
                    </div>
                    
                    <div class="p-6">
                        <form method="POST" action="/?route=admin&page=db_export" class="space-y-6">
                            <div>
                                <h3 class="text-lg font-medium text-white mb-4">Selecciona las tablas a exportar</h3>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <?php foreach ($tables as $table): ?>
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox" 
                                            id="table_<?php echo htmlspecialchars($table); ?>" 
                                            name="export_tables[]" 
                                            value="<?php echo htmlspecialchars($table); ?>" 
                                            class="w-4 h-4 text-orange-500 bg-gray-700 border-gray-600 rounded focus:ring-orange-500"
                                            checked
                                        >
                                        <label for="table_<?php echo htmlspecialchars($table); ?>" class="ml-2 text-sm font-medium text-gray-300">
                                            <?php echo htmlspecialchars($table); ?> 
                                            <span class="text-xs text-gray-400">(<?php echo number_format($db_info['tables'][$table]['rows'] ?? 0); ?> filas)</span>
                                        </label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <?php if (empty($tables)): ?>
                                <div class="p-4 bg-gray-800 rounded-lg text-center">
                                    <p class="text-gray-400">No hay tablas disponibles para exportar.</p>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" name="export_db" class="btn btn-primary">
                                    <i class="ph ph-database-export"></i> Preparar Exportación
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <?php else: ?>
                <!-- Export Methods -->
                <div class="card mb-8">
                    <div class="card-header">
                        <h2 class="text-xl font-bold text-white flex items-center gap-2">
                            <i class="ph ph-database-export text-orange-500"></i> Exportar Datos
                        </h2>
                    </div>
                    
                    <div class="p-6">
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-white mb-4">Resumen de la exportación</h3>
                            
                            <div class="bg-gray-800 p-4 rounded-lg">
                                <ul class="space-y-2">
                                    <?php foreach ($export_data as $table => $data): ?>
                                    <li class="flex items-center justify-between">
                                        <span class="text-gray-300"><?php echo htmlspecialchars($table); ?></span>
                                        <span class="text-gray-400"><?php echo number_format($data['count'] ?? 0); ?> filas</span>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Download SQL -->
                            <div class="bg-gray-800 p-6 rounded-lg">
                                <h3 class="text-lg font-medium text-white mb-4 flex items-center gap-2">
                                    <i class="ph ph-file-sql text-orange-500"></i> Descargar SQL
                                </h3>
                                
                                <p class="text-gray-300 mb-4">Descarga un archivo SQL con la estructura y datos de las tablas seleccionadas.</p>
                                
                                <form method="POST" action="/?route=admin&page=db_export">
                                    <input type="hidden" name="export_data" value="<?php echo htmlspecialchars(json_encode($export_data)); ?>">
                                    
                                    <button type="submit" name="download_sql" class="btn btn-primary w-full">
                                        <i class="ph ph-download"></i> Descargar SQL
                                    </button>
                                </form>
                            </div>
                            
                            <!-- Export to Remote DB -->
                            <div class="bg-gray-800 p-6 rounded-lg">
                                <h3 class="text-lg font-medium text-white mb-4 flex items-center gap-2">
                                    <i class="ph ph-cloud-arrow-up text-orange-500"></i> Exportar a Base de Datos Remota
                                </h3>
                                
                                <form method="POST" action="/?route=admin&page=db_export" class="space-y-4">
                                    <input type="hidden" name="export_data" value="<?php echo htmlspecialchars(json_encode($export_data)); ?>">
                                    
                                    <div>
                                        <label for="remote_host" class="block text-sm font-medium text-gray-300 mb-1">Servidor Remoto</label>
                                        <input
                                            type="text"
                                            id="remote_host"
                                            name="remote_host"
                                            required
                                            placeholder="ejemplo.com o ************"
                                            class="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                        >
                                    </div>
                                    
                                    <div>
                                        <label for="remote_user" class="block text-sm font-medium text-gray-300 mb-1">Usuario</label>
                                        <input
                                            type="text"
                                            id="remote_user"
                                            name="remote_user"
                                            required
                                            class="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                        >
                                    </div>
                                    
                                    <div>
                                        <label for="remote_password" class="block text-sm font-medium text-gray-300 mb-1">Contraseña</label>
                                        <input
                                            type="password"
                                            id="remote_password"
                                            name="remote_password"
                                            class="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                        >
                                    </div>
                                    
                                    <div>
                                        <label for="remote_database" class="block text-sm font-medium text-gray-300 mb-1">Base de Datos</label>
                                        <input
                                            type="text"
                                            id="remote_database"
                                            name="remote_database"
                                            required
                                            class="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                        >
                                    </div>
                                    
                                    <div class="flex items-center">
                                        <input 
                                            type="checkbox" 
                                            id="truncate_tables" 
                                            name="truncate_tables" 
                                            value="yes" 
                                            class="w-4 h-4 text-orange-500 bg-gray-700 border-gray-600 rounded focus:ring-orange-500"
                                            checked
                                        >
                                        <label for="truncate_tables" class="ml-2 text-sm font-medium text-gray-300">
                                            Vaciar tablas antes de importar
                                        </label>
                                    </div>
                                    
                                    <button type="submit" name="export_to_remote" class="btn btn-primary w-full">
                                        <i class="ph ph-cloud-arrow-up"></i> Exportar
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <div class="mt-6 text-center">
                            <a href="/?route=admin&page=db_export" class="text-orange-500 hover:text-orange-400">
                                <i class="ph ph-arrow-left"></i> Volver a selección de tablas
                            </a>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Database Setup Info -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-xl font-bold text-white flex items-center gap-2">
                            <i class="ph ph-info text-orange-500"></i> Información de Configuración
                        </h2>
                    </div>
                    
                    <div class="p-6">
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-white mb-2">Configuración Actual</h3>
                            <p class="text-gray-300 mb-4">Esta es la configuración actual de la base de datos utilizada por la aplicación.</p>
                            
                            <div class="bg-gray-800 p-4 rounded-lg font-mono text-sm">
                                <pre class="text-gray-300 whitespace-pre-wrap">
// Archivo: config.php
$db_config = [
    'host'     => '<?php echo htmlspecialchars($db->host_info ? explode(' ', $db->host_info)[0] : 'localhost'); ?>',
    'username' => '<?php echo defined('DB_USER') ? DB_USER : 'usuario_db'; ?>',
    'password' => '********',
    'database' => '<?php echo htmlspecialchars($db_info['name'] ?? 'nombre_db'); ?>',
    'charset'  => 'utf8mb4'
];
                                </pre>
                            </div>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-medium text-white mb-2">Configuración Manual</h3>
                            <p class="text-gray-300 mb-4">Si necesitas configurar manualmente la conexión a la base de datos, puedes editar el archivo <code>config.php</code> en la raíz del proyecto.</p>
                            
                            <div class="bg-orange-900/20 border border-orange-500/30 p-4 rounded-lg">
                                <div class="flex items-start gap-3">
                                    <i class="ph ph-info text-orange-500 text-xl mt-0.5"></i>
                                    <div>
                                        <p class="font-medium mb-2 text-white">Recomendación</p>
                                        <p class="text-sm text-gray-300">Para entornos de producción, asegúrate de utilizar un usuario de base de datos con permisos limitados y una contraseña segura.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add any JavaScript needed for the database export page
document.addEventListener('DOMContentLoaded', function() {
    // Select/deselect all tables
    const selectAllCheckbox = document.getElementById('select_all_tables');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const tableCheckboxes = document.querySelectorAll('input[name="export_tables[]"]');
            tableCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });
    }
});
</script>
