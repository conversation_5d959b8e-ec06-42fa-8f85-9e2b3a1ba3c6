<?php
// Check if logged in
if(!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: https://abla.lat/?route=admin');
    exit;
}

// Database connection
$db = connectDB();
if (is_array($db)) {
    // Connection error
    $error = "Error de conexión a la base de datos: " . $db['error'];
    include 'admin/db_init_error.php';
    exit;
}

// Process initialization
$message = '';
$success = false;
$tables_created = [];
$content_added = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['init_db'])) {
    // Initialize database
    $add_sample_content = isset($_POST['add_sample_content']) && $_POST['add_sample_content'] === 'yes';

    // Use the initializeDatabase function
    $result = initializeDatabase($db);

    if ($result['success']) {
        $success = true;
        $tables_created = $result['created_tables'];
        $content_added = $result['sample_data_added'];

        // Set success message
        if (!empty($tables_created)) {
            $message = "Inicialización completada. Tablas creadas: " . implode(", ", $tables_created);
            if (!empty($content_added)) {
                $message .= "<br>Contenido agregado: " . count($content_added) . " elementos.";
            }
        } else {
            $message = "No se crearon nuevas tablas. Es posible que ya existan.";
        }
    } else {
        // Set error message
        $message = "Error durante la inicialización: ";
        if (!empty($result['errors'])) {
            $message .= implode("<br>", $result['errors']);
        }
    }
}

// Check which tables exist
$existing_tables = [];
$required_tables = ['content', 'initiatives', 'events', 'contacts', 'users', 'themes', 'messages'];

foreach ($required_tables as $table) {
    $result = $db->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        $existing_tables[] = $table;
    }
}

$missing_tables = array_diff($required_tables, $existing_tables);
?>

<div class="bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-white">Inicialización de Base de Datos</h1>
            <a href="/?route=admin" class="btn btn-secondary">
                <i class="ph ph-arrow-left"></i> Volver al Dashboard
            </a>
        </div>

        <?php if (!empty($message)): ?>
        <div class="alert <?php echo $success ? 'alert-success' : 'alert-error'; ?> mb-8">
            <div class="alert-icon">
                <i class="ph <?php echo $success ? 'ph-check-circle' : 'ph-warning-circle'; ?>"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title"><?php echo $success ? 'Éxito' : 'Error'; ?></div>
                <div class="alert-message"><?php echo $message; ?></div>
            </div>
        </div>
        <?php endif; ?>

        <div class="card mb-8">
            <div class="card-header">
                <h2 class="text-2xl font-bold text-white flex items-center gap-2">
                    <i class="ph ph-database text-orange-500"></i> Estado de la Base de Datos
                </h2>
            </div>

            <div class="p-6">
                <div class="mb-6">
                    <h3 class="text-xl font-bold mb-4">Tablas Existentes</h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <?php foreach ($required_tables as $table): ?>
                        <div class="p-4 rounded-lg <?php echo in_array($table, $existing_tables) ? 'bg-green-900/20 border border-green-500/30' : 'bg-red-900/20 border border-red-500/30'; ?>">
                            <div class="flex items-center gap-2">
                                <i class="ph <?php echo in_array($table, $existing_tables) ? 'ph-check-circle text-green-500' : 'ph-x-circle text-red-500'; ?>"></i>
                                <span class="font-medium"><?php echo $table; ?></span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <?php if (!empty($missing_tables)): ?>
                <div class="bg-orange-900/20 border border-orange-500/30 p-4 rounded-lg mb-6">
                    <div class="flex items-start gap-3">
                        <i class="ph ph-warning-circle text-orange-500 text-xl mt-0.5"></i>
                        <div>
                            <p class="font-medium mb-2">Faltan tablas en la base de datos</p>
                            <p class="text-sm text-gray-300">Las siguientes tablas son necesarias para el funcionamiento correcto del sitio:</p>
                            <ul class="list-disc list-inside mt-2 text-sm text-gray-300">
                                <?php foreach ($missing_tables as $table): ?>
                                <li><?php echo $table; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <form method="POST" action="/?route=admin&page=db_init" class="bg-gray-800 p-6 rounded-lg">
                    <h3 class="text-xl font-bold mb-4">Inicializar Base de Datos</h3>

                    <p class="mb-4 text-gray-300">Esta acción creará las tablas necesarias para el funcionamiento del sitio. Si las tablas ya existen, no se modificarán.</p>

                    <div class="mb-4">
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="add_sample_content" value="yes" class="form-checkbox h-5 w-5 text-orange-500" checked>
                            <span class="text-gray-300">Agregar contenido de ejemplo</span>
                        </label>
                        <p class="text-sm text-gray-400 mt-1 ml-7">Incluye iniciativas, eventos y un usuario administrador predeterminado.</p>
                    </div>

                    <div class="flex justify-between">
                        <?php if (count($existing_tables) > 0): ?>
                        <a href="/?route=admin&page=db_export" class="btn btn-secondary">
                            <i class="ph ph-database-export"></i> Exportar Base de Datos
                        </a>
                        <?php else: ?>
                        <div></div> <!-- Empty div for flex spacing -->
                        <?php endif; ?>

                        <button type="submit" name="init_db" class="btn btn-primary">
                            <i class="ph ph-database-plus"></i> Inicializar Base de Datos
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <?php if (!empty($content_added)): ?>
        <div class="card">
            <div class="card-header">
                <h2 class="text-2xl font-bold text-white flex items-center gap-2">
                    <i class="ph ph-list-checks text-orange-500"></i> Contenido Agregado
                </h2>
            </div>

            <div class="p-6">
                <ul class="space-y-2">
                    <?php foreach ($content_added as $item): ?>
                    <li class="flex items-center gap-2">
                        <i class="ph ph-check-circle text-green-500"></i>
                        <span><?php echo $item; ?></span>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
