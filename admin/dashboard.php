<?php
// Check if logged in
if(!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: https://abla.lat/?route=admin');
    exit;
}

// Process logout
if (isset($_GET['action']) && $_GET['action'] === 'logout') {
    session_unset();
    session_destroy();
    header('Location: https://abla.lat/?route=admin');
    exit;
}

// Database connection
$db = connectDB();

// Process form submissions
$message = '';
$success = false;

// Check if a specific admin page is requested
$admin_page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';

// Include the requested page
if ($admin_page !== 'dashboard') {
    $page_file = __DIR__ . '/' . $admin_page . '.php';
    if (file_exists($page_file)) {
        include $page_file;
        exit;
    }
}

// Handle content update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_content'])) {
    $section = $_POST['section'] ?? '';
    $title = $_POST['title'] ?? '';
    $subtitle = $_POST['subtitle'] ?? '';
    $content = $_POST['content'] ?? '';
    $id = $_POST['id'] ?? '';

    if (empty($section) || empty($title)) {
        $message = 'La sección y el título son obligatorios.';
    } else {
        if (!empty($id)) {
            // Update existing content
            $stmt = $db->prepare("UPDATE content SET title = ?, subtitle = ?, content = ? WHERE id = ?");
            $stmt->bind_param("sssi", $title, $subtitle, $content, $id);
        } else {
            // Insert new content
            $stmt = $db->prepare("INSERT INTO content (section, title, subtitle, content) VALUES (?, ?, ?, ?)");
            $stmt->bind_param("ssss", $section, $title, $subtitle, $content);
        }

        if ($stmt->execute()) {
            $success = true;
            $message = 'Contenido actualizado correctamente.';
        } else {
            $message = 'Error al actualizar el contenido: ' . $db->error;
        }
        $stmt->close();
    }
}

// HTML sanitization function for initiative descriptions
function sanitizeInitiativeHTML($html) {
    if (empty($html)) {
        return '';
    }

    // Remove any script tags and dangerous content
    $html = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $html);
    $html = preg_replace('/on\w+="[^"]*"/i', '', $html); // Remove event handlers
    $html = preg_replace('/javascript:/i', '', $html); // Remove javascript: URLs

    // Define allowed tags with their allowed attributes
    $allowed_tags = [
        'strong' => [],
        'b' => [],
        'em' => [],
        'i' => [],
        'a' => ['href']
    ];

    // Simple regex-based sanitization
    // First, extract all HTML tags
    preg_match_all('/<\/?([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>/i', $html, $matches, PREG_OFFSET_CAPTURE);

    $offset = 0;
    $result = '';

    foreach ($matches[0] as $index => $match) {
        $fullTag = $match[0];
        $tagPosition = $match[1];
        $tagName = strtolower($matches[1][$index][0]);

        // Add text before this tag
        $result .= substr($html, $offset, $tagPosition - $offset);

        // Check if tag is allowed
        if (array_key_exists($tagName, $allowed_tags)) {
            if (strpos($fullTag, '</') === 0) {
                // Closing tag - just add it
                $result .= $fullTag;
            } else {
                // Opening tag - sanitize attributes
                if ($tagName === 'a') {
                    // Special handling for links
                    if (preg_match('/href\s*=\s*["\']([^"\']*)["\']/', $fullTag, $hrefMatch)) {
                        $href = $hrefMatch[1];
                        if (preg_match('/^https?:\/\//', $href)) {
                            $result .= '<a href="' . htmlspecialchars($href) . '" target="_blank" rel="noopener noreferrer">';
                        } else {
                            $result .= '<a>';
                        }
                    } else {
                        $result .= '<a>';
                    }
                } else {
                    // Simple tags without attributes
                    $result .= '<' . $tagName . '>';
                }
            }
        } else {
            // Tag not allowed - skip it (don't add to result)
        }

        $offset = $tagPosition + strlen($fullTag);
    }

    // Add remaining text
    $result .= substr($html, $offset);

    return $result;
}

// Handle initiative update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_initiative'])) {
    $name = $_POST['name'] ?? '';
    $instagram_user = $_POST['instagram_user'] ?? '';
    $instagram_link = $_POST['instagram_link'] ?? '';
    $twitter_user = $_POST['twitter_user'] ?? '';
    $twitter_link = $_POST['twitter_link'] ?? '';
    $avatar_url = $_POST['avatar_url'] ?? '';
    $description = $_POST['description'] ?? '';
    $id = $_POST['id'] ?? '';

    // Sanitize the description HTML
    $description = sanitizeInitiativeHTML($description);

    if (empty($name) || (empty($instagram_user) && empty($twitter_user))) {
        $message = 'El nombre y al menos un usuario de redes sociales (Instagram o X) son obligatorios.';
    } else {
        // Include avatar fetcher
        require_once '../includes/avatar_fetcher.php';

        // Try to automatically fetch avatar
        $avatar_result = null;
        $avatar_source = 'fallback';

        // Try Instagram first if available
        if (!empty($instagram_user)) {
            $avatar_result = getInstagramAvatar($instagram_user);
            if ($avatar_result['success']) {
                $avatar_url = $avatar_result['avatar_url'];
                $avatar_source = 'instagram';
            }
        }

        // Try Twitter if Instagram failed and Twitter is available
        if ((!$avatar_result || !$avatar_result['success']) && !empty($twitter_user)) {
            $avatar_result = getTwitterAvatar($twitter_user);
            if ($avatar_result['success']) {
                $avatar_url = $avatar_result['avatar_url'];
                $avatar_source = 'twitter';
            }
        }

        // Generate default avatar if all methods failed
        if (!$avatar_result || !$avatar_result['success']) {
            $username = $instagram_user ?: $twitter_user ?: $name;
            $platform = !empty($instagram_user) ? 'instagram' : 'twitter';
            $avatar_url = getDefaultAvatar($username, $platform);
            $avatar_source = $platform . '_generated';
        }

        if (!empty($id)) {
            // Update existing initiative
            $stmt = $db->prepare("UPDATE initiatives SET name = ?, instagram_user = ?, instagram_link = ?, twitter_user = ?, twitter_link = ?, avatar_url = ?, avatar_source = ?, description = ? WHERE id = ?");
            $stmt->bind_param("ssssssssi", $name, $instagram_user, $instagram_link, $twitter_user, $twitter_link, $avatar_url, $avatar_source, $description, $id);
        } else {
            // Insert new initiative
            $stmt = $db->prepare("INSERT INTO initiatives (name, instagram_user, instagram_link, twitter_user, twitter_link, avatar_url, avatar_source, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssssssss", $name, $instagram_user, $instagram_link, $twitter_user, $twitter_link, $avatar_url, $avatar_source, $description);
        }

        if ($stmt->execute()) {
            $success = true;
            $message = 'Iniciativa actualizada correctamente.';

            // Add avatar status to message
            if ($avatar_result && $avatar_result['success']) {
                $source_name = $avatar_source === 'instagram' ? 'Instagram' : 'X (Twitter)';
                $message .= " Avatar obtenido desde $source_name.";
            } else {
                $message .= ' Avatar generado automáticamente.';
            }
        } else {
            $message = 'Error al actualizar la iniciativa: ' . $db->error;
        }
        $stmt->close();
    }
}

// Handle avatar refresh AJAX request
if (isset($_GET['action']) && $_GET['action'] === 'refresh_avatar' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    $input = json_decode(file_get_contents('php://input'), true);
    $initiative_id = $input['initiative_id'] ?? null;

    if (!$initiative_id) {
        echo json_encode(['success' => false, 'error' => 'ID de iniciativa requerido']);
        exit;
    }

    // Get initiative data
    $stmt = $db->prepare("SELECT * FROM initiatives WHERE id = ?");
    $stmt->bind_param("i", $initiative_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $initiative = $result->fetch_assoc();
    $stmt->close();

    if (!$initiative) {
        echo json_encode(['success' => false, 'error' => 'Iniciativa no encontrada']);
        exit;
    }

    // Include avatar fetcher
    require_once '../includes/avatar_fetcher.php';

    // Attempt to fetch and update avatar
    $avatar_result = fetchAndUpdateAvatar($db, $initiative_id, $initiative['instagram_user'], $initiative['twitter_user']);

    echo json_encode($avatar_result);
    exit;
}

// Handle delete
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['type']) && isset($_GET['id'])) {
    $type = $_GET['type'];
    $id = (int)$_GET['id'];

    if ($type === 'content') {
        $stmt = $db->prepare("DELETE FROM content WHERE id = ?");
    } elseif ($type === 'initiative') {
        $stmt = $db->prepare("DELETE FROM initiatives WHERE id = ?");
    }

    if (isset($stmt)) {
        $stmt->bind_param("i", $id);
        if ($stmt->execute()) {
            $success = true;
            $message = 'Elemento eliminado correctamente.';
        } else {
            $message = 'Error al eliminar el elemento: ' . $db->error;
        }
        $stmt->close();
    }
}

// Check if tables exist
$tables_exist = true;
$required_tables = ['content', 'initiatives', 'events', 'users', 'themes'];
$missing_tables = [];

foreach ($required_tables as $table) {
    try {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows === 0) {
            $missing_tables[] = $table;
        }
    } catch (Exception $e) {
        $missing_tables[] = $table;
    }
}

// Only show database initialization if critical tables are missing
$critical_tables = ['content', 'initiatives', 'users'];
$critical_missing = array_intersect($critical_tables, $missing_tables);

if (!empty($critical_missing)) {
    $tables_exist = false;
    $message = 'Faltan tablas críticas en la base de datos: ' . implode(', ', $critical_missing) . '. Por favor inicialice la base de datos.';
    $success = false;
} elseif (!empty($missing_tables)) {
    // Show warning for non-critical missing tables
    $message = 'Algunas tablas opcionales no existen: ' . implode(', ', $missing_tables) . '. Considere ejecutar la inicialización de base de datos para obtener todas las funcionalidades.';
    $success = false;
}

// Get content data
$content_data = [];
if ($tables_exist) {
    try {
        $result = $db->query("SELECT * FROM content ORDER BY section, id DESC");
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $content_data[] = $row;
            }
        }
    } catch (Exception $e) {
        // Table might exist but have issues
        $message = 'Error al obtener contenido: ' . $e->getMessage();
        $success = false;
    }
}

// Get initiatives data
$initiatives_data = [];
if ($tables_exist) {
    try {
        $result = $db->query("SELECT * FROM initiatives ORDER BY id DESC");
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $initiatives_data[] = $row;
            }
        }
    } catch (Exception $e) {
        // Table might exist but have issues
        if (empty($message)) {
            $message = 'Error al obtener iniciativas: ' . $e->getMessage();
            $success = false;
        }
    }
}
?>

<div class="bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white">Panel de Administración</h1>
                <div class="flex mt-2 space-x-4">
                    <a href="/?route=admin" class="text-gray-300 hover:text-white <?php echo $admin_page === 'dashboard' ? 'border-b-2 border-orange-500' : ''; ?>">
                        Dashboard
                    </a>
                    <a href="/?route=admin&page=themes" class="text-gray-300 hover:text-white <?php echo $admin_page === 'themes' ? 'border-b-2 border-orange-500' : ''; ?>">
                        Temas
                    </a>
                    <a href="/?route=admin&page=inbox" class="text-gray-300 hover:text-white <?php echo $admin_page === 'inbox' ? 'border-b-2 border-orange-500' : ''; ?> relative">
                        Mensajes
                        <?php
                        // Get unread message count
                        $unread_count = 0;
                        if (!is_array($db)) {
                            $unread_result = $db->query("SELECT COUNT(*) as count FROM messages WHERE status = 'unread'");
                            if ($unread_result) {
                                $unread_count = $unread_result->fetch_assoc()['count'];
                            }
                        }
                        if ($unread_count > 0):
                        ?>
                        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                            <?php echo $unread_count > 99 ? '99+' : $unread_count; ?>
                        </span>
                        <?php endif; ?>
                    </a>
                    <div class="relative group">
                        <a href="/?route=admin&page=db_init" class="text-gray-300 hover:text-white <?php echo in_array($admin_page, ['db_init', 'db_export']) ? 'border-b-2 border-orange-500' : ''; ?>">
                            Base de Datos
                        </a>
                        <div class="absolute left-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-10 hidden group-hover:block">
                            <a href="/?route=admin&page=db_init" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                                <i class="ph ph-database-plus mr-2"></i> Inicializar DB
                            </a>
                            <a href="/?route=admin&page=db_export" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                                <i class="ph ph-database-export mr-2"></i> Exportar DB
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <span class="text-gray-400 mr-4">Bienvenido, <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="/?route=admin&action=logout" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded">Cerrar sesión</a>
            </div>
        </div>

        <?php if (!empty($message)): ?>
        <div class="alert <?php echo $success ? 'alert-success' : 'alert-error'; ?> mb-8">
            <div class="alert-icon">
                <i class="ph <?php echo $success ? 'ph-check-circle' : 'ph-warning-circle'; ?>"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title"><?php echo $success ? 'Éxito' : 'Error'; ?></div>
                <div class="alert-message"><?php echo $message; ?></div>
                <?php if (!$tables_exist): ?>
                <div class="mt-2">
                    <a href="/?route=admin&page=db_init" class="btn btn-primary btn-sm">
                        <i class="ph ph-database-plus"></i> Inicializar Base de Datos
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!$tables_exist): ?>
        <div class="card mb-8">
            <div class="card-header">
                <h2 class="text-2xl font-bold text-white flex items-center gap-2">
                    <i class="ph ph-database text-orange-500"></i> Base de Datos
                </h2>
            </div>

            <div class="p-6">
                <div class="bg-orange-900/20 border border-orange-500/30 p-4 rounded-lg mb-6">
                    <div class="flex items-start gap-3">
                        <i class="ph ph-warning-circle text-orange-500 text-xl mt-0.5"></i>
                        <div>
                            <p class="font-medium mb-2">Tablas de base de datos no encontradas</p>
                            <p class="text-sm text-gray-300">Es necesario inicializar la base de datos para el correcto funcionamiento del sitio.</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <a href="/?route=admin&page=db_init" class="btn btn-primary">
                        <i class="ph ph-database-plus"></i> Inicializar Base de Datos
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions (Always show database management) -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-white mb-6">Gestión de Base de Datos</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/?route=admin&page=db_init" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-database text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Inicializar DB</span>
                    <span class="text-xs text-gray-400">Crear tablas</span>
                </a>

                <a href="/update_db.php" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-wrench text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Actualizar DB</span>
                    <span class="text-xs text-gray-400">Migrar cambios</span>
                </a>

                <a href="/debug_db.php" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-bug text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Debug DB</span>
                    <span class="text-xs text-gray-400">Diagnosticar</span>
                </a>

                <a href="/check_db.php" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-check-circle text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Verificar DB</span>
                    <span class="text-xs text-gray-400">Estado rápido</span>
                </a>
            </div>
        </div>

        <?php if (empty($critical_missing)): ?>
        <!-- Quick Actions -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-white mb-6">Acciones Rápidas</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/?route=admin&page=inbox" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-envelope text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Mensajes</span>
                    <?php if ($unread_count > 0): ?>
                    <span class="text-xs text-orange-400"><?php echo $unread_count; ?> sin leer</span>
                    <?php endif; ?>
                </a>

                <a href="/?route=admin&page=themes" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-palette text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Temas</span>
                    <span class="text-xs text-gray-400">Personalizar</span>
                </a>

                <a href="/?route=admin&page=db_init" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-database text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Base de Datos</span>
                    <span class="text-xs text-gray-400">Gestionar</span>
                </a>

                <a href="/" target="_blank" class="flex flex-col items-center p-4 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="ph ph-globe text-orange-500 text-2xl mb-2"></i>
                    <span class="text-white text-sm font-medium">Ver Sitio</span>
                    <span class="text-xs text-gray-400">Nueva pestaña</span>
                </a>
            </div>
        </div>

        <!-- Dashboard Widgets -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <!-- Quick Stats -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h2 class="text-2xl font-bold text-white mb-6">Estadísticas Rápidas</h2>

                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Iniciativas</p>
                                <p class="text-2xl font-bold text-white"><?php echo count($initiatives_data); ?></p>
                            </div>
                            <div class="bg-orange-500 rounded-full p-3">
                                <i class="ph ph-handshake text-white text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400 text-sm">Contenidos</p>
                                <p class="text-2xl font-bold text-white"><?php echo count($content_data); ?></p>
                            </div>
                            <div class="bg-orange-500 rounded-full p-3">
                                <i class="ph ph-article text-white text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Info -->
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-white">Base de Datos</h2>
                    <div class="flex gap-2">
                        <a href="/?route=admin&page=db_init" class="text-orange-500 hover:text-orange-400 text-sm flex items-center gap-1">
                            <i class="ph ph-database-plus"></i> Gestionar
                        </a>
                        <a href="/?route=admin&page=db_export" class="text-blue-500 hover:text-blue-400 text-sm flex items-center gap-1">
                            <i class="ph ph-database-export"></i> Exportar
                        </a>
                    </div>
                </div>

                <?php
                // Get database info
                $db_info = [];
                $tables_count = 0;
                $db_size = 0;

                // Get database name
                $db_name = '';
                $result = $db->query("SELECT DATABASE()");
                if ($result && $row = $result->fetch_row()) {
                    $db_name = $row[0];
                }

                // Get all existing tables
                $existing_tables = [];
                $result = $db->query("SHOW TABLES");
                if ($result) {
                    $tables_count = $result->num_rows;
                    while ($row = $result->fetch_row()) {
                        $existing_tables[] = $row[0];
                    }

                    // Get database size
                    $size_result = $db->query("SELECT SUM(data_length + index_length) AS size FROM information_schema.TABLES WHERE table_schema = '$db_name'");
                    if ($size_result && $size_row = $size_result->fetch_assoc()) {
                        $db_size = $size_row['size'];
                    }
                }

                // Format database size
                function formatSize($bytes) {
                    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
                    $i = 0;
                    while ($bytes >= 1024 && $i < count($units) - 1) {
                        $bytes /= 1024;
                        $i++;
                    }
                    return round($bytes, 2) . ' ' . $units[$i];
                }

                // All expected tables
                $all_expected_tables = ['content', 'initiatives', 'events', 'users', 'themes', 'messages', 'contacts'];
                ?>

                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <p class="text-gray-400">Base de datos:</p>
                        <p class="text-white font-mono"><?php echo htmlspecialchars($db_name); ?></p>
                    </div>

                    <div class="flex items-center justify-between">
                        <p class="text-gray-400">Tablas:</p>
                        <p class="text-white font-mono"><?php echo $tables_count; ?></p>
                    </div>

                    <div class="flex items-center justify-between">
                        <p class="text-gray-400">Tamaño:</p>
                        <p class="text-white font-mono"><?php echo formatSize($db_size); ?></p>
                    </div>

                    <div class="flex items-center justify-between">
                        <p class="text-gray-400">Estado:</p>
                        <p class="text-green-500 flex items-center gap-1">
                            <i class="ph ph-check-circle"></i> Conectado
                        </p>
                    </div>

                    <!-- Table Status -->
                    <div class="mt-4 pt-4 border-t border-gray-700">
                        <p class="text-gray-400 text-sm mb-2">Estado de tablas:</p>
                        <div class="grid grid-cols-2 gap-1 text-xs">
                            <?php foreach ($all_expected_tables as $table): ?>
                            <div class="flex items-center gap-1">
                                <?php if (in_array($table, $existing_tables)): ?>
                                <i class="ph ph-check-circle text-green-500"></i>
                                <span class="text-green-400"><?php echo $table; ?></span>
                                <?php else: ?>
                                <i class="ph ph-x-circle text-red-500"></i>
                                <span class="text-red-400"><?php echo $table; ?></span>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <?php if (!empty($missing_tables)): ?>
                    <div class="mt-4 pt-4 border-t border-gray-700">
                        <div class="bg-orange-900/20 border border-orange-500/30 p-3 rounded">
                            <p class="text-orange-400 text-sm font-medium mb-1">
                                <i class="ph ph-warning-circle mr-1"></i>
                                Tablas faltantes: <?php echo count($missing_tables); ?>
                            </p>
                            <div class="flex gap-2">
                                <a href="/?route=admin&page=db_init" class="text-orange-500 hover:text-orange-400 text-xs flex items-center gap-1">
                                    <i class="ph ph-database-plus"></i> Inicializar
                                </a>
                                <a href="/update_db.php" class="text-blue-500 hover:text-blue-400 text-xs flex items-center gap-1">
                                    <i class="ph ph-wrench"></i> Actualizar
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Messages -->
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-white">Mensajes Recientes</h2>
                    <a href="/?route=admin&page=inbox" class="text-orange-500 hover:text-orange-400 text-sm flex items-center gap-1">
                        <i class="ph ph-envelope"></i> Ver todos
                    </a>
                </div>

                <?php
                // Get recent messages
                $recent_messages = [];
                if (!is_array($db)) {
                    $recent_result = $db->query("SELECT * FROM messages ORDER BY created_at DESC LIMIT 5");
                    if ($recent_result) {
                        while ($row = $recent_result->fetch_assoc()) {
                            $recent_messages[] = $row;
                        }
                    }
                }
                ?>

                <div class="space-y-4">
                    <?php if (empty($recent_messages)): ?>
                    <div class="text-center py-4">
                        <i class="ph ph-envelope-simple text-gray-600 text-4xl mb-2"></i>
                        <p class="text-gray-400">No hay mensajes recientes</p>
                    </div>
                    <?php else: ?>
                    <?php foreach ($recent_messages as $msg): ?>
                    <div class="flex items-start gap-3 p-3 bg-gray-700 rounded-lg">
                        <div class="bg-<?php echo $msg['status'] === 'unread' ? 'red' : ($msg['status'] === 'read' ? 'yellow' : 'green'); ?>-500 rounded-full p-2 mt-1">
                            <i class="ph ph-envelope text-white text-sm"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <p class="text-white font-medium truncate"><?php echo htmlspecialchars($msg['name']); ?></p>
                                <span class="text-xs text-gray-400"><?php echo date('d/m H:i', strtotime($msg['created_at'])); ?></span>
                            </div>
                            <p class="text-sm text-gray-400 truncate"><?php echo htmlspecialchars($msg['subject']); ?></p>
                            <p class="text-xs text-gray-500 truncate"><?php echo htmlspecialchars(substr($msg['message'], 0, 60)) . (strlen($msg['message']) > 60 ? '...' : ''); ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- System Status -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-white mb-6">Estado del Sistema</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Database Status -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-300 text-sm">Base de Datos</span>
                        <?php if (empty($critical_missing)): ?>
                        <i class="ph ph-check-circle text-green-500"></i>
                        <?php else: ?>
                        <i class="ph ph-warning-circle text-red-500"></i>
                        <?php endif; ?>
                    </div>
                    <p class="text-xs text-gray-400">
                        <?php echo empty($critical_missing) ? 'Funcionando' : count($critical_missing) . ' tablas faltantes'; ?>
                    </p>
                </div>

                <!-- Messages System -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-300 text-sm">Mensajes</span>
                        <?php if (in_array('messages', $existing_tables)): ?>
                        <i class="ph ph-check-circle text-green-500"></i>
                        <?php else: ?>
                        <i class="ph ph-x-circle text-red-500"></i>
                        <?php endif; ?>
                    </div>
                    <p class="text-xs text-gray-400">
                        <?php echo in_array('messages', $existing_tables) ? 'Activo' : 'No disponible'; ?>
                    </p>
                </div>

                <!-- Themes System -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-300 text-sm">Temas</span>
                        <?php if (in_array('themes', $existing_tables)): ?>
                        <i class="ph ph-check-circle text-green-500"></i>
                        <?php else: ?>
                        <i class="ph ph-x-circle text-red-500"></i>
                        <?php endif; ?>
                    </div>
                    <p class="text-xs text-gray-400">
                        <?php echo in_array('themes', $existing_tables) ? 'Activo' : 'No disponible'; ?>
                    </p>
                </div>

                <!-- Content System -->
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-gray-300 text-sm">Contenido</span>
                        <?php if (in_array('content', $existing_tables) && in_array('initiatives', $existing_tables)): ?>
                        <i class="ph ph-check-circle text-green-500"></i>
                        <?php else: ?>
                        <i class="ph ph-x-circle text-red-500"></i>
                        <?php endif; ?>
                    </div>
                    <p class="text-xs text-gray-400">
                        <?php echo (in_array('content', $existing_tables) && in_array('initiatives', $existing_tables)) ? 'Activo' : 'Parcial'; ?>
                    </p>
                </div>
            </div>

            <?php if (!empty($missing_tables)): ?>
            <div class="mt-6 bg-orange-900/20 border border-orange-500/30 p-4 rounded-lg">
                <div class="flex items-start gap-3">
                    <i class="ph ph-info-circle text-orange-500 text-lg mt-0.5"></i>
                    <div>
                        <p class="text-orange-400 font-medium mb-1">Funcionalidades limitadas</p>
                        <p class="text-sm text-gray-300 mb-3">
                            Algunas funciones pueden no estar disponibles debido a tablas faltantes en la base de datos.
                        </p>
                        <div class="flex gap-2">
                            <a href="/?route=admin&page=db_init" class="btn btn-primary btn-sm">
                                <i class="ph ph-database-plus"></i> Inicializar DB
                            </a>
                            <a href="/update_db.php" class="btn btn-secondary btn-sm">
                                <i class="ph ph-wrench"></i> Actualizar DB
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-white mb-6">Gestión de Contenido</h2>

            <!-- Tabs -->
            <div x-data="{ activeTab: 'content' }">
                <div class="border-b border-gray-700 mb-6">
                    <ul class="flex flex-wrap -mb-px">
                        <li class="mr-2">
                            <button
                                @click="activeTab = 'content'"
                                :class="{ 'border-orange-500 text-orange-500': activeTab === 'content', 'border-transparent text-gray-400 hover:text-gray-300': activeTab !== 'content' }"
                                class="inline-block py-2 px-4 border-b-2 font-medium text-sm"
                            >
                                Contenido
                            </button>
                        </li>
                        <li class="mr-2">
                            <button
                                @click="activeTab = 'initiatives'"
                                :class="{ 'border-orange-500 text-orange-500': activeTab === 'initiatives', 'border-transparent text-gray-400 hover:text-gray-300': activeTab !== 'initiatives' }"
                                class="inline-block py-2 px-4 border-b-2 font-medium text-sm"
                            >
                                Iniciativas
                            </button>
                        </li>
                    </ul>
                </div>

                <!-- Content Tab -->
                <div x-show="activeTab === 'content'">
                    <button
                        class="mb-6 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded"
                        onclick="document.getElementById('content-form').reset(); document.getElementById('content-form-title').textContent = 'Agregar Contenido';"
                    >
                        Agregar Nuevo Contenido
                    </button>

                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-gray-700 rounded-lg overflow-hidden">
                            <thead class="bg-gray-600">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">ID</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Sección</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Título</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Subtítulo</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-600">
                                <?php foreach ($content_data as $content): ?>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo $content['id']; ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo htmlspecialchars($content['section']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo htmlspecialchars($content['title']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo htmlspecialchars($content['subtitle']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300">
                                        <button
                                            class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded mr-2"
                                            onclick="editContent(<?php echo htmlspecialchars(json_encode($content)); ?>)"
                                        >
                                            Editar
                                        </button>
                                        <a
                                            href="/?route=admin&action=delete&type=content&id=<?php echo $content['id']; ?>"
                                            class="bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded"
                                            onclick="return confirm('¿Estás seguro de que deseas eliminar este contenido?');"
                                        >
                                            Eliminar
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php if (empty($content_data)): ?>
                                <tr>
                                    <td colspan="5" class="px-4 py-3 text-sm text-gray-300 text-center">No hay contenido disponible.</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Content Form -->
                    <div class="mt-8 bg-gray-700 p-6 rounded-lg">
                        <h3 id="content-form-title" class="text-xl font-bold text-white mb-4">Agregar Contenido</h3>
                        <form id="content-form" method="POST" action="/?route=admin" class="space-y-4">
                            <input type="hidden" name="id" id="content-id">

                            <div>
                                <label for="section" class="block text-sm font-medium text-gray-300 mb-1">Sección</label>
                                <select
                                    id="section"
                                    name="section"
                                    required
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                                    <option value="hero">Hero (Inicio)</option>
                                    <option value="about">Sobre Nosotros</option>
                                    <option value="team">Equipo</option>
                                    <option value="eventos">Eventos</option>
                                </select>
                            </div>

                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-300 mb-1">Título</label>
                                <input
                                    type="text"
                                    id="title"
                                    name="title"
                                    required
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div>
                                <label for="subtitle" class="block text-sm font-medium text-gray-300 mb-1">Subtítulo</label>
                                <input
                                    type="text"
                                    id="subtitle"
                                    name="subtitle"
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div>
                                <label for="content" class="block text-sm font-medium text-gray-300 mb-1">Contenido</label>
                                <textarea
                                    id="content"
                                    name="content"
                                    rows="5"
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                ></textarea>
                            </div>

                            <div>
                                <button
                                    type="submit"
                                    name="update_content"
                                    class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded"
                                >
                                    Guardar
                                </button>
                                <button
                                    type="button"
                                    onclick="document.getElementById('content-form').reset(); document.getElementById('content-id').value = '';"
                                    class="bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded ml-2"
                                >
                                    Cancelar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Initiatives Tab -->
                <div x-show="activeTab === 'initiatives'">
                    <button
                        class="mb-6 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded"
                        onclick="document.getElementById('initiative-form').reset(); document.getElementById('initiative-form-title').textContent = 'Agregar Iniciativa';"
                    >
                        Agregar Nueva Iniciativa
                    </button>

                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-gray-700 rounded-lg overflow-hidden">
                            <thead class="bg-gray-600">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Avatar</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Nombre</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Instagram</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">X (Twitter)</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-600">
                                <?php
                                // Include avatar fetcher for display
                                require_once '../includes/avatar_fetcher.php';

                                foreach ($initiatives_data as $initiative):
                                    $avatar_url = getAvatarForDisplay($initiative);
                                ?>
                                <tr>
                                    <td class="px-4 py-3 text-sm text-gray-300">
                                        <img src="<?php echo htmlspecialchars($avatar_url); ?>"
                                             alt="Avatar"
                                             class="w-10 h-10 rounded-full object-cover border-2 border-gray-500"
                                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2QjcyODAiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTMuMSAyIDE0IDIuOSAxNCA0QzE0IDUuMSAxMy4xIDYgMTIgNkMxMC45IDYgMTAgNS4xIDEwIDRDMTAgMi45IDEwLjkgMiAxMiAyWk0yMSAxOVYyMEgzVjE5QzMgMTYuMzMgOC42NyAxNSAxMiAxNUMxNS4zMyAxNSAyMSAxNi4zMyAyMSAxOVpNMTIgMTNDOS43OSAxMyA4IDExLjIxIDggOUM4IDYuNzkgOS43OSA1IDEyIDVDMTQuMjEgNSAxNiA2Ljc5IDE2IDlDMTYgMTEuMjEgMTQuMjEgMTMgMTIgMTNaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+'">
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-300"><?php echo htmlspecialchars($initiative['name']); ?></td>
                                    <td class="px-4 py-3 text-sm text-gray-300">
                                        <?php if (!empty($initiative['instagram_user'])): ?>
                                            <div class="flex items-center gap-2">
                                                <i class="ph ph-instagram-logo text-pink-500"></i>
                                                <?php echo htmlspecialchars($initiative['instagram_user']); ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-gray-500">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-300">
                                        <?php if (!empty($initiative['twitter_user'])): ?>
                                            <div class="flex items-center gap-2">
                                                <i class="ph ph-x-logo text-blue-400"></i>
                                                <?php echo htmlspecialchars($initiative['twitter_user']); ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-gray-500">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-300">
                                        <button
                                            class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-2 rounded mr-2 text-xs"
                                            onclick="editInitiative(<?php echo htmlspecialchars(json_encode($initiative)); ?>)"
                                        >
                                            Editar
                                        </button>
                                        <button
                                            class="bg-green-500 hover:bg-green-600 text-white py-1 px-2 rounded mr-2 text-xs"
                                            onclick="refreshAvatar(<?php echo $initiative['id']; ?>)"
                                            title="Actualizar avatar"
                                        >
                                            🔄
                                        </button>
                                        <a
                                            href="/?route=admin&action=delete&type=initiative&id=<?php echo $initiative['id']; ?>"
                                            class="bg-red-500 hover:bg-red-600 text-white py-1 px-2 rounded text-xs"
                                            onclick="return confirm('¿Estás seguro de que deseas eliminar esta iniciativa?');"
                                        >
                                            Eliminar
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                <?php if (empty($initiatives_data)): ?>
                                <tr>
                                    <td colspan="4" class="px-4 py-3 text-sm text-gray-300 text-center">No hay iniciativas disponibles.</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Initiative Form -->
                    <div class="mt-8 bg-gray-700 p-6 rounded-lg">
                        <h3 id="initiative-form-title" class="text-xl font-bold text-white mb-4">Agregar Iniciativa</h3>
                        <form id="initiative-form" method="POST" action="/?route=admin" class="space-y-4">
                            <input type="hidden" name="id" id="initiative-id">

                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Nombre</label>
                                <input
                                    type="text"
                                    id="name"
                                    name="name"
                                    required
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div>
                                <label for="instagram_user" class="block text-sm font-medium text-gray-300 mb-1">Usuario de Instagram</label>
                                <input
                                    type="text"
                                    id="instagram_user"
                                    name="instagram_user"
                                    required
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div>
                                <label for="instagram_link" class="block text-sm font-medium text-gray-300 mb-1">Enlace de Instagram</label>
                                <input
                                    type="url"
                                    id="instagram_link"
                                    name="instagram_link"
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                >
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="twitter_user" class="block text-sm font-medium text-gray-300 mb-1">Usuario de X (Twitter)</label>
                                    <input
                                        type="text"
                                        id="twitter_user"
                                        name="twitter_user"
                                        placeholder="@usuario"
                                        class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                    >
                                </div>

                                <div>
                                    <label for="twitter_link" class="block text-sm font-medium text-gray-300 mb-1">Enlace de X (Twitter)</label>
                                    <input
                                        type="url"
                                        id="twitter_link"
                                        name="twitter_link"
                                        class="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                                    >
                                </div>
                            </div>

                            <!-- Avatar Info Section -->
                            <div class="bg-gray-800 p-4 rounded-lg border border-gray-600">
                                <label class="block text-sm font-medium text-gray-300 mb-2">Avatar de la Iniciativa</label>

                                <div class="flex items-center gap-4">
                                    <!-- Current Avatar Preview -->
                                    <div id="current-avatar-preview" class="hidden">
                                        <img id="current-avatar-img" src="" alt="Avatar actual" class="w-16 h-16 rounded-full object-cover border-2 border-orange-500/30">
                                    </div>

                                    <div class="flex-1">
                                        <p class="text-sm text-gray-300 mb-2">
                                            🤖 <strong>Carga automática:</strong> El avatar se obtendrá automáticamente desde Instagram o X cuando guardes la iniciativa.
                                        </p>

                                        <div class="flex gap-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-pink-600/20 text-pink-300">
                                                📷 Instagram
                                            </span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-600/20 text-blue-300">
                                                🐦 X (Twitter)
                                            </span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-600/20 text-gray-300">
                                                🎨 Generado
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <p class="text-xs text-gray-500 mt-3">
                                    💡 <strong>Nota:</strong> Si no se puede obtener el avatar automáticamente, se generará uno personalizado con las iniciales.
                                </p>
                            </div>

                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-300 mb-1">Descripción</label>

                                <!-- Formatting Toolbar -->
                                <div class="flex gap-2 mb-2 p-2 bg-gray-700 rounded-t-md border border-gray-600">
                                    <button type="button" onclick="formatText('description', 'bold')" class="px-2 py-1 bg-gray-600 hover:bg-gray-500 text-white text-xs rounded" title="Negrita">
                                        <strong>B</strong>
                                    </button>
                                    <button type="button" onclick="formatText('description', 'italic')" class="px-2 py-1 bg-gray-600 hover:bg-gray-500 text-white text-xs rounded italic" title="Cursiva">
                                        I
                                    </button>
                                    <button type="button" onclick="formatText('description', 'link')" class="px-2 py-1 bg-gray-600 hover:bg-gray-500 text-white text-xs rounded" title="Enlace">
                                        🔗
                                    </button>
                                    <div class="border-l border-gray-500 mx-2"></div>
                                    <button type="button" onclick="previewDescription()" class="px-2 py-1 bg-orange-600 hover:bg-orange-500 text-white text-xs rounded" title="Vista previa">
                                        👁️ Vista previa
                                    </button>
                                </div>

                                <textarea
                                    id="description"
                                    name="description"
                                    rows="4"
                                    class="w-full p-2 bg-gray-800 border border-gray-600 rounded-b-md text-white focus:outline-none focus:border-orange-500 font-mono text-sm"
                                    placeholder="Escribe la descripción aquí..."
                                ></textarea>

                                <!-- Preview Area -->
                                <div id="description-preview" class="hidden mt-2 p-3 bg-gray-700 border border-gray-600 rounded-md">
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-gray-300">Vista previa:</span>
                                        <button type="button" onclick="hidePreview()" class="text-xs text-gray-400 hover:text-white">✕ Cerrar</button>
                                    </div>
                                    <div id="description-preview-content" class="text-gray-200 text-sm"></div>
                                </div>

                                <div class="mt-2 text-xs text-gray-400">
                                    <p><strong>Formato permitido:</strong></p>
                                    <ul class="list-disc list-inside mt-1 space-y-1">
                                        <li><code>&lt;strong&gt;texto&lt;/strong&gt;</code> o <code>&lt;b&gt;texto&lt;/b&gt;</code> para <strong>negrita</strong></li>
                                        <li><code>&lt;em&gt;texto&lt;/em&gt;</code> o <code>&lt;i&gt;texto&lt;/i&gt;</code> para <em>cursiva</em></li>
                                        <li><code>&lt;a href="URL"&gt;texto&lt;/a&gt;</code> para <a href="#" class="text-orange-500 underline">enlaces</a></li>
                                    </ul>
                                </div>
                            </div>

                            <div>
                                <button
                                    type="submit"
                                    name="update_initiative"
                                    class="bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded"
                                >
                                    Guardar
                                </button>
                                <button
                                    type="button"
                                    onclick="document.getElementById('initiative-form').reset(); document.getElementById('initiative-id').value = '';"
                                    class="bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded ml-2"
                                >
                                    Cancelar
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function editContent(content) {
        document.getElementById('content-id').value = content.id;
        document.getElementById('section').value = content.section;
        document.getElementById('title').value = content.title;
        document.getElementById('subtitle').value = content.subtitle || '';
        document.getElementById('content').value = content.content || '';
        document.getElementById('content-form-title').textContent = 'Editar Contenido';

        // Scroll to form
        document.getElementById('content-form').scrollIntoView({ behavior: 'smooth' });
    }

    function editInitiative(initiative) {
        document.getElementById('initiative-id').value = initiative.id;
        document.getElementById('name').value = initiative.name;
        document.getElementById('instagram_user').value = initiative.instagram_user || '';
        document.getElementById('instagram_link').value = initiative.instagram_link || '';
        document.getElementById('twitter_user').value = initiative.twitter_user || '';
        document.getElementById('twitter_link').value = initiative.twitter_link || '';
        document.getElementById('avatar_url').value = initiative.avatar_url || '';
        document.getElementById('description').value = initiative.description || '';
        document.getElementById('initiative-form-title').textContent = 'Editar Iniciativa';

        // Show current avatar if exists
        if (initiative.avatar_url) {
            showCurrentAvatar(initiative.avatar_url);
        } else {
            hideCurrentAvatar();
        }

        // Scroll to form
        document.getElementById('initiative-form').scrollIntoView({ behavior: 'smooth' });
    }

    // HTML Formatting Functions
    function formatText(textareaId, format) {
        const textarea = document.getElementById(textareaId);
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);

        let formattedText = '';

        switch(format) {
            case 'bold':
                if (selectedText) {
                    formattedText = `<strong>${selectedText}</strong>`;
                } else {
                    formattedText = '<strong>texto en negrita</strong>';
                }
                break;
            case 'italic':
                if (selectedText) {
                    formattedText = `<em>${selectedText}</em>`;
                } else {
                    formattedText = '<em>texto en cursiva</em>';
                }
                break;
            case 'link':
                const url = prompt('Ingresa la URL del enlace:', 'https://');
                if (url && url !== 'https://') {
                    const linkText = selectedText || 'texto del enlace';
                    formattedText = `<a href="${url}">${linkText}</a>`;
                } else {
                    return; // User cancelled or didn't provide URL
                }
                break;
        }

        // Replace selected text with formatted text
        const newValue = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);
        textarea.value = newValue;

        // Set cursor position after the inserted text
        const newCursorPos = start + formattedText.length;
        textarea.setSelectionRange(newCursorPos, newCursorPos);
        textarea.focus();
    }

    function previewDescription() {
        const textarea = document.getElementById('description');
        const preview = document.getElementById('description-preview');
        const previewContent = document.getElementById('description-preview-content');

        if (textarea.value.trim() === '') {
            alert('Escribe algo en la descripción para ver la vista previa.');
            return;
        }

        // Sanitize and allow only specific HTML tags
        const sanitizedHTML = sanitizeHTML(textarea.value);
        previewContent.innerHTML = sanitizedHTML;
        preview.classList.remove('hidden');
    }

    function hidePreview() {
        document.getElementById('description-preview').classList.add('hidden');
    }

    function sanitizeHTML(html) {
        // Create a temporary div to parse HTML
        const temp = document.createElement('div');
        temp.innerHTML = html;

        // Remove all elements except allowed ones
        const allowedTags = ['strong', 'b', 'em', 'i', 'a'];
        const allElements = temp.querySelectorAll('*');

        allElements.forEach(element => {
            if (!allowedTags.includes(element.tagName.toLowerCase())) {
                // Replace disallowed tags with their text content
                element.replaceWith(document.createTextNode(element.textContent));
            } else if (element.tagName.toLowerCase() === 'a') {
                // Ensure links have proper attributes and open in new tab
                element.setAttribute('target', '_blank');
                element.setAttribute('rel', 'noopener noreferrer');

                // Validate href attribute
                const href = element.getAttribute('href');
                if (!href || (!href.startsWith('http://') && !href.startsWith('https://'))) {
                    element.removeAttribute('href');
                }
            }
        });

        return temp.innerHTML;
    }

    // Avatar refresh function
    function refreshAvatar(initiativeId) {
        if (!confirm('¿Actualizar el avatar de esta iniciativa?')) {
            return;
        }

        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '⏳';
        button.disabled = true;

        fetch('/?route=admin&action=refresh_avatar', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ initiative_id: initiativeId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Avatar actualizado correctamente desde ' + data.source);
                location.reload(); // Refresh page to show new avatar
            } else {
                alert('Error al actualizar avatar: ' + (data.error || 'Error desconocido'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al actualizar avatar');
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
</script>
