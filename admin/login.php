<?php
require_once '../security_log.php';

// Get return URL if specified
$return_url = $_GET['return'] ?? 'https://abla.lat/?route=admin';
$return_url = filter_var($return_url, FILTER_SANITIZE_URL);

// Validate return URL to prevent open redirects
if (!empty($return_url)) {
    $parsed_url = parse_url($return_url);
    $allowed_hosts = ['abla.lat', 'www.abla.lat', 'localhost', '127.0.0.1'];

    if (isset($parsed_url['host']) && !in_array($parsed_url['host'], $allowed_hosts)) {
        $return_url = 'https://abla.lat/?route=admin';
    }
} else {
    $return_url = 'https://abla.lat/?route=admin';
}

// Check if already logged in
if(isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    // Add success message for already logged in users
    $_SESSION['login_success'] = 'Ya estás conectado al panel de administración.';
    header('Location: ' . $return_url);
    exit;
}

// DDoS Protection and Rate Limiting
$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
$client_fingerprint = md5($client_ip . $user_agent);

// Initialize session arrays if they don't exist
if (!isset($_SESSION['login_attempts'])) $_SESSION['login_attempts'] = [];
if (!isset($_SESSION['captcha_attempts'])) $_SESSION['captcha_attempts'] = [];
if (!isset($_SESSION['ip_attempts'])) $_SESSION['ip_attempts'] = [];

// Get current attempts for this IP/fingerprint
$ip_attempts = $_SESSION['ip_attempts'][$client_fingerprint] ?? ['count' => 0, 'last_attempt' => 0, 'blocked_until' => 0];
$login_attempts = $_SESSION['login_attempts'][$client_fingerprint] ?? ['count' => 0, 'last_attempt' => 0];
$captcha_attempts = $_SESSION['captcha_attempts'][$client_fingerprint] ?? ['count' => 0, 'last_attempt' => 0];

// Progressive rate limiting
$current_time = time();
$show_captcha = false;
$error = '';

// Check if IP is temporarily blocked
if ($ip_attempts['blocked_until'] > $current_time) {
    $remaining_time = ceil(($ip_attempts['blocked_until'] - $current_time) / 60);
    $error = "IP temporalmente bloqueada. Inténtalo de nuevo en {$remaining_time} minutos.";

    // Log blocked access attempt
    logRateLimitExceeded('IP_BLOCKED', [
        'remaining_time_minutes' => $remaining_time,
        'block_reason' => 'Too many failed login attempts'
    ]);
}
// Progressive blocking: 3 attempts = captcha, 5 attempts = 15 min block, 10 attempts = 1 hour block
elseif ($login_attempts['count'] >= 10 && ($current_time - $login_attempts['last_attempt']) < 3600) {
    $error = 'Demasiados intentos fallidos. Inténtalo de nuevo en 1 hora.';
    $ip_attempts['blocked_until'] = $current_time + 3600;
}
elseif ($login_attempts['count'] >= 5 && ($current_time - $login_attempts['last_attempt']) < 900) {
    $error = 'Demasiados intentos fallidos. Inténtalo de nuevo en 15 minutos.';
    $ip_attempts['blocked_until'] = $current_time + 900;
}
elseif ($login_attempts['count'] >= 3) {
    $show_captcha = true;
}

// Process login if not blocked
if (empty($error) && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login_submit'])) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $captcha_input = trim($_POST['captcha'] ?? '');
    $honeypot = $_POST['website'] ?? '';

    // Honeypot check - if filled, it's likely a bot
    if (!empty($honeypot)) {
        // Log potential bot attempt
        logHoneypotTrigger([
            'honeypot_value' => $honeypot,
            'username_attempted' => $username
        ]);

        // Fake processing delay to confuse bots
        sleep(2);

        $error = 'Error de validación. Por favor, inténtalo de nuevo.';
        $login_attempts['count']++;
        $login_attempts['last_attempt'] = $current_time;
    }
    // Validate required fields
    elseif (empty($username) || empty($password)) {
        $error = 'Por favor ingresa usuario y contraseña.';
        $login_attempts['count']++;
        $login_attempts['last_attempt'] = $current_time;
    }
    // Validate CAPTCHA if required
    elseif ($show_captcha && (empty($captcha_input) || !isset($_SESSION['login_captcha']) ||
             strtoupper($captcha_input) !== strtoupper($_SESSION['login_captcha']))) {
        $error = 'El código de verificación es incorrecto. Por favor, inténtalo de nuevo.';
        $captcha_attempts['count']++;
        $captcha_attempts['last_attempt'] = $current_time;
        $login_attempts['count']++;
        $login_attempts['last_attempt'] = $current_time;

        // Log CAPTCHA failure
        logCaptchaFailure([
            'username_attempted' => $username,
            'captcha_expected' => $_SESSION['login_captcha'] ?? 'none',
            'captcha_provided' => $captcha_input,
            'attempt_count' => $captcha_attempts['count']
        ]);

        // Clear the captcha to force a new one
        unset($_SESSION['login_captcha']);
    } else {
        // Check credentials against database
        $db = connectDB();
        if (is_array($db)) {
            $error = 'Error de conexión a la base de datos. Inténtalo más tarde.';
        } else {
            $stmt = $db->prepare("SELECT id, username, password, last_login FROM users WHERE username = ?");
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows === 1) {
                $user = $result->fetch_assoc();
                if (password_verify($password, $user['password'])) {
                    // Login successful - reset all attempts for this fingerprint
                    unset($_SESSION['login_attempts'][$client_fingerprint]);
                    unset($_SESSION['captcha_attempts'][$client_fingerprint]);
                    unset($_SESSION['ip_attempts'][$client_fingerprint]);
                    unset($_SESSION['login_captcha']);

                    // Log successful login
                    logLoginAttempt($username, true, [
                        'login_method' => 'password',
                        'captcha_required' => $show_captcha,
                        'return_url' => $return_url
                    ]);

                    // Set session variables with enhanced security
                    $_SESSION['admin_logged_in'] = true;
                    $_SESSION['admin_id'] = $user['id'];
                    $_SESSION['admin_username'] = $user['username'];
                    $_SESSION['login_time'] = time();
                    $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
                    $_SESSION['login_ip'] = $_SERVER['REMOTE_ADDR'] ?? '';

                    // Update last login time in database
                    $update_stmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                    $update_stmt->bind_param("i", $user['id']);
                    $update_stmt->execute();
                    $update_stmt->close();

                    // Set success message
                    $_SESSION['login_success'] = '¡Bienvenido de vuelta, ' . htmlspecialchars($user['username']) . '!';

                    // Redirect to return URL or dashboard
                    header('Location: ' . $return_url);
                    exit;
                } else {
                    $error = 'Credenciales incorrectas.';
                    $login_attempts['count']++;
                    $login_attempts['last_attempt'] = $current_time;

                    // Log failed login attempt
                    logLoginAttempt($username, false, [
                        'reason' => 'invalid_password',
                        'attempt_count' => $login_attempts['count'],
                        'captcha_required' => $show_captcha
                    ]);

                    unset($_SESSION['login_captcha']); // Force new captcha
                }
            } else {
                $error = 'Credenciales incorrectas.';
                $login_attempts['count']++;
                $login_attempts['last_attempt'] = $current_time;

                // Log failed login attempt
                logLoginAttempt($username, false, [
                    'reason' => 'user_not_found',
                    'attempt_count' => $login_attempts['count'],
                    'captcha_required' => $show_captcha
                ]);

                unset($_SESSION['login_captcha']); // Force new captcha
            }

            $stmt->close();
        }
    }

    // Update session arrays with current attempts
    $_SESSION['login_attempts'][$client_fingerprint] = $login_attempts;
    $_SESSION['captcha_attempts'][$client_fingerprint] = $captcha_attempts;
    $_SESSION['ip_attempts'][$client_fingerprint] = $ip_attempts;
}

// Generate CAPTCHA if needed
if ($show_captcha && !isset($_SESSION['login_captcha'])) {
    $chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $captcha = '';
    for ($i = 0; $i < 6; $i++) {
        $captcha .= $chars[rand(0, strlen($chars) - 1)];
    }
    $_SESSION['login_captcha'] = $captcha;
}

// Clean up old session data (prevent memory bloat)
$cleanup_time = $current_time - 7200; // 2 hours
foreach (['login_attempts', 'captcha_attempts', 'ip_attempts'] as $session_key) {
    if (isset($_SESSION[$session_key]) && is_array($_SESSION[$session_key])) {
        foreach ($_SESSION[$session_key] as $fingerprint => $data) {
            if (isset($data['last_attempt']) && $data['last_attempt'] < $cleanup_time) {
                unset($_SESSION[$session_key][$fingerprint]);
            }
        }
    }
}
?>

<div class="min-h-screen flex items-center justify-center bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-white">
                Panel de Administración
            </h2>
            <p class="mt-2 text-center text-sm text-gray-400">
                Ingresa tus credenciales para acceder
            </p>
        </div>
        
        <?php if (!empty($error)): ?>
        <div class="bg-red-800 border border-red-600 text-white p-4 rounded-md flex items-start gap-3">
            <i class="ph ph-warning-circle text-red-300 text-xl mt-0.5 flex-shrink-0"></i>
            <div>
                <div class="font-medium">Error de inicio de sesión</div>
                <div class="text-sm text-red-200 mt-1"><?php echo htmlspecialchars($error); ?></div>
                <?php
                $current_attempts = $login_attempts['count'] ?? 0;
                if ($current_attempts >= 1 && $current_attempts < 10):
                ?>
                <div class="text-xs text-red-300 mt-2">
                    <div>Intentos fallidos: <?php echo $current_attempts; ?>/10</div>
                    <?php if ($current_attempts >= 3): ?>
                    <div class="mt-1">⚠️ Se requiere verificación de seguridad</div>
                    <?php endif; ?>
                    <?php if ($current_attempts >= 5): ?>
                    <div class="mt-1">🔒 Próximo bloqueo temporal en <?php echo (5 - ($current_attempts - 5)); ?> intentos</div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($show_captcha): ?>
        <div class="bg-orange-900/20 border border-orange-500/30 text-orange-200 p-4 rounded-md flex items-start gap-3">
            <i class="ph ph-shield-check text-orange-400 text-xl mt-0.5 flex-shrink-0"></i>
            <div>
                <div class="font-medium">Verificación de seguridad requerida</div>
                <div class="text-sm text-orange-300 mt-1">
                    Se han detectado múltiples intentos de inicio de sesión. Por favor, completa la verificación de seguridad.
                </div>
            </div>
        </div>
        <?php endif; ?>

        <form class="mt-8 space-y-6" method="POST" action="/?route=admin" id="loginForm">
            <!-- Hidden return URL field -->
            <?php if (!empty($_GET['return'])): ?>
            <input type="hidden" name="return_url" value="<?php echo htmlspecialchars($_GET['return']); ?>">
            <?php endif; ?>

            <div class="rounded-md shadow-sm -space-y-px">
                <div>
                    <label for="username" class="sr-only">Usuario</label>
                    <input id="username" name="username" type="text" required
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-700 placeholder-gray-500 text-white bg-gray-800 rounded-t-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm transition-colors"
                           placeholder="Usuario"
                           autocomplete="username">
                </div>
                <div>
                    <label for="password" class="sr-only">Contraseña</label>
                    <input id="password" name="password" type="password" required
                           class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-700 placeholder-gray-500 text-white bg-gray-800 rounded-b-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm transition-colors"
                           placeholder="Contraseña"
                           autocomplete="current-password">
                </div>
            </div>

            <!-- CAPTCHA Section -->
            <?php if ($show_captcha): ?>
            <div class="bg-gray-800 border border-gray-700 rounded-lg p-4">
                <label class="block text-sm font-medium text-gray-300 mb-3">
                    <i class="ph ph-shield-check mr-2 text-orange-500"></i>
                    Verificación de seguridad
                </label>

                <div class="flex items-center gap-4 mb-3">
                    <div id="loginCaptchaDisplay" class="bg-gray-900 px-4 py-2 rounded font-mono text-xl tracking-widest text-orange-500 select-none min-w-[120px] text-center">
                        <?php echo $_SESSION['login_captcha'] ?? ''; ?>
                    </div>
                    <button type="button" id="refreshLoginCaptcha" class="p-2 bg-gray-700 rounded hover:bg-gray-600 transition-colors" title="Generar nuevo código">
                        <i class="ph ph-arrows-clockwise"></i>
                    </button>
                </div>

                <div>
                    <input
                        type="text"
                        id="captcha"
                        name="captcha"
                        class="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-orange-500 transition-colors"
                        placeholder="Ingresa el código que ves arriba"
                        autocomplete="off"
                        required
                        maxlength="6"
                    >
                </div>
            </div>
            <?php endif; ?>

            <div>
                <button type="submit" name="login_submit" id="loginButton"
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
                    <span id="loginButtonText">
                        <i class="ph ph-sign-in mr-2"></i>
                        Iniciar sesión
                    </span>
                    <span id="loginButtonLoading" class="hidden">
                        <i class="ph ph-spinner ph-spin mr-2"></i>
                        Iniciando sesión...
                    </span>
                </button>
            </div>

            <div class="text-center">
                <a href="/?route=home" class="text-sm text-orange-500 hover:text-orange-400 transition-colors">
                    <i class="ph ph-arrow-left mr-1"></i>
                    Volver al sitio
                </a>
            </div>
        </form>

        <!-- Login Form JavaScript -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginButton = document.getElementById('loginButton');
            const loginButtonText = document.getElementById('loginButtonText');
            const loginButtonLoading = document.getElementById('loginButtonLoading');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            const captchaInput = document.getElementById('captcha');
            const captchaDisplay = document.getElementById('loginCaptchaDisplay');
            const refreshCaptchaBtn = document.getElementById('refreshLoginCaptcha');

            // Focus on appropriate field
            if (captchaInput && captchaInput.offsetParent !== null) {
                // CAPTCHA is visible, focus on it if username and password are filled
                if (usernameInput.value && passwordInput.value) {
                    captchaInput.focus();
                } else {
                    usernameInput.focus();
                }
            } else {
                usernameInput.focus();
            }

            // CAPTCHA refresh functionality
            if (refreshCaptchaBtn && captchaDisplay) {
                refreshCaptchaBtn.addEventListener('click', function() {
                    // Generate new CAPTCHA
                    fetch('/get_login_captcha.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.captcha) {
                            captchaDisplay.textContent = data.captcha;
                            if (captchaInput) {
                                captchaInput.value = '';
                                captchaInput.focus();
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error refreshing CAPTCHA:', error);
                        // Fallback: generate client-side CAPTCHA
                        const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                        let captcha = '';
                        for (let i = 0; i < 6; i++) {
                            captcha += chars[Math.floor(Math.random() * chars.length)];
                        }
                        captchaDisplay.textContent = captcha;
                        if (captchaInput) {
                            captchaInput.value = '';
                            captchaInput.focus();
                        }
                    });
                });
            }

            // Handle form submission
            loginForm.addEventListener('submit', function(e) {
                // Show loading state
                loginButton.disabled = true;
                loginButtonText.classList.add('hidden');
                loginButtonLoading.classList.remove('hidden');

                // Add visual feedback
                loginButton.classList.add('opacity-75');

                // If this is a client-side validation failure, re-enable the button
                setTimeout(function() {
                    if (loginForm.checkValidity && !loginForm.checkValidity()) {
                        resetLoginButton();
                    }
                }, 100);
            });

            function resetLoginButton() {
                loginButton.disabled = false;
                loginButtonText.classList.remove('hidden');
                loginButtonLoading.classList.add('hidden');
                loginButton.classList.remove('opacity-75');
            }

            // Reset button state if there's an error (page reload)
            <?php if (!empty($error)): ?>
            resetLoginButton();
            <?php endif; ?>

            // Add enter key support for better UX
            passwordInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    loginForm.submit();
                }
            });

            // Clear any previous error styling on input
            const allInputs = [usernameInput, passwordInput];
            if (captchaInput) allInputs.push(captchaInput);

            allInputs.forEach(input => {
                input.addEventListener('input', function() {
                    this.classList.remove('border-red-500');
                    this.classList.add('border-gray-700');
                });
            });

            // Add error styling if there was an error
            <?php if (!empty($error)): ?>
            usernameInput.classList.add('border-red-500');
            passwordInput.classList.add('border-red-500');
            <?php if ($show_captcha): ?>
            if (captchaInput) captchaInput.classList.add('border-red-500');
            <?php endif; ?>
            <?php endif; ?>

            // Enhanced keyboard navigation
            if (captchaInput) {
                captchaInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        loginForm.submit();
                    }
                });

                // Auto-uppercase CAPTCHA input
                captchaInput.addEventListener('input', function(e) {
                    this.value = this.value.toUpperCase();
                });
            }

            // Add honeypot protection (hidden field)
            const honeypot = document.createElement('input');
            honeypot.type = 'text';
            honeypot.name = 'website';
            honeypot.style.display = 'none';
            honeypot.tabIndex = -1;
            honeypot.autocomplete = 'off';
            loginForm.appendChild(honeypot);

            // Detect automated submissions
            let formInteractionTime = Date.now();
            allInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    formInteractionTime = Date.now();
                });
            });

            // Add submission timing check
            loginForm.addEventListener('submit', function(e) {
                const submissionTime = Date.now();
                const interactionDuration = submissionTime - formInteractionTime;

                // If form is submitted too quickly (less than 2 seconds), it might be automated
                if (interactionDuration < 2000) {
                    console.warn('Rapid form submission detected');
                }

                // Check honeypot
                if (honeypot.value !== '') {
                    e.preventDefault();
                    console.warn('Honeypot triggered');
                    return false;
                }
            });
        });
        </script>
    </div>
</div>
