<?php
// Check if logged in
if(!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: https://abla.lat/?route=admin');
    exit;
}

// Process form submissions
$message = '';
$success = false;

// Handle theme activation
if (isset($_GET['action']) && $_GET['action'] === 'activate' && isset($_GET['id'])) {
    $theme_id = (int)$_GET['id'];

    if (setActiveTheme($theme_id)) {
        $success = true;
        $message = 'Tema activado correctamente.';
    } else {
        $message = 'Error al activar el tema.';
    }
}

// Handle theme deletion
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $theme_id = (int)$_GET['id'];

    if (deleteTheme($theme_id)) {
        $success = true;
        $message = 'Tema eliminado correctamente.';
    } else {
        $message = 'Error al eliminar el tema.';
    }
}

// Handle theme creation/update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_theme'])) {
    $theme_data = [
        'name' => $_POST['name'] ?? '',
        'primary_color' => $_POST['primary_color'] ?? '#F7931A',
        'primary_hover' => $_POST['primary_hover'] ?? '#FF9500',
        'primary_light' => $_POST['primary_light'] ?? '#FEF4E8',
        'dark_bg' => $_POST['dark_bg'] ?? '#090C14',
        'card_bg' => $_POST['card_bg'] ?? '#13161F',
        'overlay_bg' => $_POST['overlay_bg'] ?? '#00000099',
        'light_text' => $_POST['light_text'] ?? '#FFFFFF',
        'muted_text' => $_POST['muted_text'] ?? 'rgba(255, 255, 255, 0.7)',
        'border_color' => $_POST['border_color'] ?? 'rgba(255, 255, 255, 0.1)'
    ];

    if (empty($theme_data['name'])) {
        $message = 'El nombre del tema es obligatorio.';
    } else {
        $theme_id = isset($_POST['theme_id']) && !empty($_POST['theme_id']) ? (int)$_POST['theme_id'] : null;

        if ($theme_id) {
            // Update existing theme
            if (updateTheme($theme_id, $theme_data)) {
                $success = true;
                $message = 'Tema actualizado correctamente.';

                // Activate theme if requested
                if (isset($_POST['activate']) && $_POST['activate'] === '1') {
                    setActiveTheme($theme_id);
                }
            } else {
                $message = 'Error al actualizar el tema.';
            }
        } else {
            // Create new theme
            $new_id = createTheme($theme_data);

            if ($new_id) {
                $success = true;
                $message = 'Tema creado correctamente.';

                // Activate theme if requested
                if (isset($_POST['activate']) && $_POST['activate'] === '1') {
                    setActiveTheme($new_id);
                }
            } else {
                $message = 'Error al crear el tema.';
            }
        }
    }
}

// Get all themes
$themes = getAllThemes();

// Get theme to edit if specified
$edit_theme = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $theme_id = (int)$_GET['id'];

    foreach ($themes as $theme) {
        if ($theme['id'] == $theme_id) {
            $edit_theme = $theme;
            break;
        }
    }
}
?>

<div class="bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-white">Gestión de Temas</h1>
            <a href="/?route=admin" class="btn btn-secondary">
                <i class="ph ph-arrow-left"></i> Volver al Dashboard
            </a>
        </div>

        <?php if (!empty($message)): ?>
        <div class="alert <?php echo $success ? 'alert-success' : 'alert-error'; ?>">
            <div class="alert-icon">
                <i class="ph <?php echo $success ? 'ph-check-circle' : 'ph-warning-circle'; ?>"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title"><?php echo $success ? 'Éxito' : 'Error'; ?></div>
                <div class="alert-message"><?php echo $message; ?></div>
            </div>
        </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Theme List -->
            <div class="lg:col-span-2">
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-2xl font-bold text-white flex items-center gap-2">
                            <i class="ph ph-palette text-orange-500"></i> Temas Disponibles
                        </h2>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php foreach ($themes as $theme): ?>
                        <div class="card <?php echo $theme['is_active'] ? 'card-primary' : ''; ?> p-4">
                            <div class="flex justify-between items-start mb-3">
                                <h3 class="text-lg font-bold text-white"><?php echo htmlspecialchars($theme['name']); ?></h3>
                                <?php if ($theme['is_active']): ?>
                                <span class="badge badge-primary-solid">
                                    <i class="ph ph-check badge-icon"></i> Activo
                                </span>
                                <?php endif; ?>
                            </div>

                            <!-- Color Preview -->
                            <div class="grid grid-cols-5 gap-2 mb-4">
                                <div class="h-8 rounded" style="background-color: <?php echo $theme['primary_color']; ?>;" title="Primary"></div>
                                <div class="h-8 rounded" style="background-color: <?php echo $theme['primary_hover']; ?>;" title="Primary Hover"></div>
                                <div class="h-8 rounded" style="background-color: <?php echo $theme['primary_light']; ?>;" title="Primary Light"></div>
                                <div class="h-8 rounded" style="background-color: <?php echo $theme['dark_bg']; ?>;" title="Dark Background"></div>
                                <div class="h-8 rounded" style="background-color: <?php echo $theme['card_bg']; ?>;" title="Card Background"></div>
                            </div>

                            <!-- Actions -->
                            <div class="flex justify-end space-x-2">
                                <?php if (!$theme['is_active']): ?>
                                <a href="/?route=admin&page=themes&action=activate&id=<?php echo $theme['id']; ?>" class="btn btn-primary btn-sm">
                                    <i class="ph ph-check"></i> Activar
                                </a>
                                <?php endif; ?>
                                <a href="/?route=admin&page=themes&action=edit&id=<?php echo $theme['id']; ?>" class="btn btn-secondary btn-sm">
                                    <i class="ph ph-pencil"></i> Editar
                                </a>
                                <?php if (!$theme['is_active']): ?>
                                <a href="/?route=admin&page=themes&action=delete&id=<?php echo $theme['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('¿Estás seguro de que deseas eliminar este tema?');">
                                    <i class="ph ph-trash"></i> Eliminar
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>

                        <?php if (empty($themes)): ?>
                        <div class="col-span-2 bg-gray-700 rounded-lg p-4 text-center text-gray-300">
                            No hay temas disponibles.
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Theme Form -->
            <div class="lg:col-span-1">
                <div class="card sticky top-4">
                    <div class="card-header">
                        <h2 class="text-2xl font-bold text-white flex items-center gap-2">
                            <i class="ph <?php echo $edit_theme ? 'ph-pencil-simple' : 'ph-plus'; ?> text-orange-500"></i>
                            <?php echo $edit_theme ? 'Editar Tema' : 'Crear Nuevo Tema'; ?>
                        </h2>
                    </div>

                    <form method="POST" action="/?route=admin&page=themes" class="space-y-4">
                        <?php if ($edit_theme): ?>
                        <input type="hidden" name="theme_id" value="<?php echo $edit_theme['id']; ?>">
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="name" class="form-label">Nombre del Tema</label>
                            <div class="input-icon-wrapper">
                                <div class="input-icon">
                                    <i class="ph ph-tag"></i>
                                </div>
                                <input
                                    type="text"
                                    id="name"
                                    name="name"
                                    value="<?php echo $edit_theme ? htmlspecialchars($edit_theme['name']) : ''; ?>"
                                    required
                                    class="form-input pl-10"
                                    placeholder="Ej: Tema Oscuro"
                                >
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="primary_color" class="block text-sm font-medium text-gray-300 mb-1">Color Primario</label>
                                <div class="flex">
                                    <input
                                        type="color"
                                        id="primary_color"
                                        name="primary_color"
                                        value="<?php echo $edit_theme ? $edit_theme['primary_color'] : '#F7931A'; ?>"
                                        class="h-10 w-10 rounded-l-md border-r-0 border border-gray-600"
                                    >
                                    <input
                                        type="text"
                                        id="primary_color_text"
                                        value="<?php echo $edit_theme ? $edit_theme['primary_color'] : '#F7931A'; ?>"
                                        class="flex-grow p-2 bg-gray-700 border border-gray-600 rounded-r-md text-white focus:outline-none focus:border-orange-500"
                                        oninput="document.getElementById('primary_color').value = this.value"
                                    >
                                </div>
                            </div>

                            <div>
                                <label for="primary_hover" class="block text-sm font-medium text-gray-300 mb-1">Hover Primario</label>
                                <div class="flex">
                                    <input
                                        type="color"
                                        id="primary_hover"
                                        name="primary_hover"
                                        value="<?php echo $edit_theme ? $edit_theme['primary_hover'] : '#FF9500'; ?>"
                                        class="h-10 w-10 rounded-l-md border-r-0 border border-gray-600"
                                    >
                                    <input
                                        type="text"
                                        id="primary_hover_text"
                                        value="<?php echo $edit_theme ? $edit_theme['primary_hover'] : '#FF9500'; ?>"
                                        class="flex-grow p-2 bg-gray-700 border border-gray-600 rounded-r-md text-white focus:outline-none focus:border-orange-500"
                                        oninput="document.getElementById('primary_hover').value = this.value"
                                    >
                                </div>
                            </div>

                            <div>
                                <label for="primary_light" class="block text-sm font-medium text-gray-300 mb-1">Primario Claro</label>
                                <div class="flex">
                                    <input
                                        type="color"
                                        id="primary_light"
                                        name="primary_light"
                                        value="<?php echo $edit_theme ? $edit_theme['primary_light'] : '#FEF4E8'; ?>"
                                        class="h-10 w-10 rounded-l-md border-r-0 border border-gray-600"
                                    >
                                    <input
                                        type="text"
                                        id="primary_light_text"
                                        value="<?php echo $edit_theme ? $edit_theme['primary_light'] : '#FEF4E8'; ?>"
                                        class="flex-grow p-2 bg-gray-700 border border-gray-600 rounded-r-md text-white focus:outline-none focus:border-orange-500"
                                        oninput="document.getElementById('primary_light').value = this.value"
                                    >
                                </div>
                            </div>

                            <div>
                                <label for="dark_bg" class="block text-sm font-medium text-gray-300 mb-1">Fondo Oscuro</label>
                                <div class="flex">
                                    <input
                                        type="color"
                                        id="dark_bg"
                                        name="dark_bg"
                                        value="<?php echo $edit_theme ? $edit_theme['dark_bg'] : '#090C14'; ?>"
                                        class="h-10 w-10 rounded-l-md border-r-0 border border-gray-600"
                                    >
                                    <input
                                        type="text"
                                        id="dark_bg_text"
                                        value="<?php echo $edit_theme ? $edit_theme['dark_bg'] : '#090C14'; ?>"
                                        class="flex-grow p-2 bg-gray-700 border border-gray-600 rounded-r-md text-white focus:outline-none focus:border-orange-500"
                                        oninput="document.getElementById('dark_bg').value = this.value"
                                    >
                                </div>
                            </div>

                            <div>
                                <label for="card_bg" class="block text-sm font-medium text-gray-300 mb-1">Fondo de Tarjeta</label>
                                <div class="flex">
                                    <input
                                        type="color"
                                        id="card_bg"
                                        name="card_bg"
                                        value="<?php echo $edit_theme ? $edit_theme['card_bg'] : '#13161F'; ?>"
                                        class="h-10 w-10 rounded-l-md border-r-0 border border-gray-600"
                                    >
                                    <input
                                        type="text"
                                        id="card_bg_text"
                                        value="<?php echo $edit_theme ? $edit_theme['card_bg'] : '#13161F'; ?>"
                                        class="flex-grow p-2 bg-gray-700 border border-gray-600 rounded-r-md text-white focus:outline-none focus:border-orange-500"
                                        oninput="document.getElementById('card_bg').value = this.value"
                                    >
                                </div>
                            </div>

                            <div>
                                <label for="light_text" class="block text-sm font-medium text-gray-300 mb-1">Texto Claro</label>
                                <div class="flex">
                                    <input
                                        type="color"
                                        id="light_text"
                                        name="light_text"
                                        value="<?php echo $edit_theme ? $edit_theme['light_text'] : '#FFFFFF'; ?>"
                                        class="h-10 w-10 rounded-l-md border-r-0 border border-gray-600"
                                    >
                                    <input
                                        type="text"
                                        id="light_text_text"
                                        value="<?php echo $edit_theme ? $edit_theme['light_text'] : '#FFFFFF'; ?>"
                                        class="flex-grow p-2 bg-gray-700 border border-gray-600 rounded-r-md text-white focus:outline-none focus:border-orange-500"
                                        oninput="document.getElementById('light_text').value = this.value"
                                    >
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="overlay_bg" class="block text-sm font-medium text-gray-300 mb-1">Fondo de Superposición</label>
                            <input
                                type="text"
                                id="overlay_bg"
                                name="overlay_bg"
                                value="<?php echo $edit_theme ? $edit_theme['overlay_bg'] : '#00000099'; ?>"
                                class="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                            >
                            <p class="text-xs text-gray-400 mt-1">Formato: #RRGGBBAA o rgba(r,g,b,a)</p>
                        </div>

                        <div>
                            <label for="muted_text" class="block text-sm font-medium text-gray-300 mb-1">Texto Atenuado</label>
                            <input
                                type="text"
                                id="muted_text"
                                name="muted_text"
                                value="<?php echo $edit_theme ? $edit_theme['muted_text'] : 'rgba(255, 255, 255, 0.7)'; ?>"
                                class="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                            >
                            <p class="text-xs text-gray-400 mt-1">Formato: rgba(r,g,b,a)</p>
                        </div>

                        <div>
                            <label for="border_color" class="block text-sm font-medium text-gray-300 mb-1">Color de Borde</label>
                            <input
                                type="text"
                                id="border_color"
                                name="border_color"
                                value="<?php echo $edit_theme ? $edit_theme['border_color'] : 'rgba(255, 255, 255, 0.1)'; ?>"
                                class="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:border-orange-500"
                            >
                            <p class="text-xs text-gray-400 mt-1">Formato: rgba(r,g,b,a)</p>
                        </div>

                        <div class="pt-2">
                            <label class="flex items-center">
                                <input
                                    type="checkbox"
                                    name="activate"
                                    value="1"
                                    <?php echo (!$edit_theme || ($edit_theme && !$edit_theme['is_active'])) ? 'checked' : ''; ?>
                                    class="mr-2"
                                >
                                <span class="text-sm text-gray-300">Activar este tema al guardar</span>
                            </label>
                        </div>

                        <div class="flex justify-between pt-4">
                            <a href="/?route=admin&page=themes" class="btn btn-secondary">
                                <i class="ph ph-x"></i> Cancelar
                            </a>
                            <button
                                type="submit"
                                name="save_theme"
                                class="btn btn-primary"
                            >
                                <i class="ph ph-floppy-disk"></i> <?php echo $edit_theme ? 'Actualizar Tema' : 'Crear Tema'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Sync color inputs
    document.addEventListener('DOMContentLoaded', function() {
        const colorInputs = [
            'primary_color',
            'primary_hover',
            'primary_light',
            'dark_bg',
            'card_bg',
            'light_text'
        ];

        colorInputs.forEach(function(id) {
            const colorInput = document.getElementById(id);
            const textInput = document.getElementById(id + '_text');

            colorInput.addEventListener('input', function() {
                textInput.value = this.value;
            });

            textInput.addEventListener('input', function() {
                colorInput.value = this.value;
            });
        });
    });
</script>
