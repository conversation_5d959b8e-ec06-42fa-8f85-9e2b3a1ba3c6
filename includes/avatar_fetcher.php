<?php
/**
 * Avatar Fetcher Utility
 *
 * This utility provides functions to fetch profile images from social media platforms.
 * Due to API restrictions, we'll use alternative methods and fallbacks.
 */

/**
 * Get Instagram profile picture URL using multiple methods
 */
function getInstagramAvatar($username) {
    // Remove @ symbol if present
    $username = ltrim($username, '@');

    // Method 1: Try Instagram's public API endpoint
    $avatar_url = fetchInstagramAvatarAPI($username);
    if ($avatar_url) {
        return [
            'success' => true,
            'avatar_url' => $avatar_url,
            'method' => 'api',
            'source' => 'instagram'
        ];
    }

    // Method 2: Try web scraping with different approach
    $avatar_url = fetchInstagramAvatarWeb($username);
    if ($avatar_url) {
        return [
            'success' => true,
            'avatar_url' => $avatar_url,
            'method' => 'web',
            'source' => 'instagram'
        ];
    }

    // Method 3: Try using Instagram's embed endpoint
    $avatar_url = fetchInstagramAvatarEmbed($username);
    if ($avatar_url) {
        return [
            'success' => true,
            'avatar_url' => $avatar_url,
            'method' => 'embed',
            'source' => 'instagram'
        ];
    }

    // Method 4: Try using third-party services
    $avatar_url = fetchInstagramAvatarThirdParty($username);
    if ($avatar_url) {
        return [
            'success' => true,
            'avatar_url' => $avatar_url,
            'method' => 'third_party',
            'source' => 'instagram'
        ];
    }

    // Fallback to a default Instagram-style avatar
    return [
        'success' => false,
        'avatar_url' => getDefaultAvatar($username, 'instagram'),
        'method' => 'fallback',
        'source' => 'instagram'
    ];
}

/**
 * Get X (Twitter) profile picture URL using multiple methods
 */
function getTwitterAvatar($username) {
    // Remove @ symbol if present
    $username = ltrim($username, '@');

    // Method 1: Try using Twitter's public API endpoints
    $avatar_url = fetchTwitterAvatarAPI($username);
    if ($avatar_url) {
        return [
            'success' => true,
            'avatar_url' => $avatar_url,
            'method' => 'api',
            'source' => 'twitter'
        ];
    }

    // Method 2: Try web scraping
    $avatar_url = fetchTwitterAvatarWeb($username);
    if ($avatar_url) {
        return [
            'success' => true,
            'avatar_url' => $avatar_url,
            'method' => 'web',
            'source' => 'twitter'
        ];
    }

    // Method 3: Try using third-party services
    $avatar_url = fetchTwitterAvatarThirdParty($username);
    if ($avatar_url) {
        return [
            'success' => true,
            'avatar_url' => $avatar_url,
            'method' => 'third_party',
            'source' => 'twitter'
        ];
    }

    // Fallback to default Twitter-style avatar
    return [
        'success' => false,
        'avatar_url' => getDefaultAvatar($username, 'twitter'),
        'method' => 'fallback',
        'source' => 'twitter'
    ];
}

/**
 * Fetch Instagram avatar using API method
 */
function fetchInstagramAvatarAPI($username) {
    // Try Instagram's public endpoints
    $endpoints = [
        "https://www.instagram.com/api/v1/users/web_profile_info/?username=$username",
        "https://i.instagram.com/api/v1/users/web_profile_info/?username=$username"
    ];

    foreach ($endpoints as $url) {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept: application/json',
                    'X-Requested-With: XMLHttpRequest'
                ],
                'timeout' => 10
            ]
        ]);

        $response = @file_get_contents($url, false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['data']['user']['profile_pic_url'])) {
                return $data['data']['user']['profile_pic_url'];
            }
        }
    }

    return false;
}

/**
 * Fetch Instagram avatar using web scraping
 */
function fetchInstagramAvatarWeb($username) {
    $url = "https://www.instagram.com/$username/";

    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language: en-US,en;q=0.5',
                'Accept-Encoding: gzip, deflate',
                'Connection: keep-alive',
            ],
            'timeout' => 10
        ]
    ]);

    $html = @file_get_contents($url, false, $context);

    if (!$html) {
        return false;
    }

    // Look for profile image in various patterns
    $patterns = [
        '/"profile_pic_url":"([^"]+)"/',
        '/"profile_pic_url_hd":"([^"]+)"/',
        '/property="og:image" content="([^"]+)"/',
        '/name="twitter:image" content="([^"]+)"/',
        '/<img[^>]+class="[^"]*profile[^"]*"[^>]+src="([^"]+)"/',
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $html, $matches)) {
            $avatar_url = $matches[1];

            // Decode JSON escaped characters
            $avatar_url = str_replace('\/', '/', $avatar_url);
            $avatar_url = str_replace('\\u0026', '&', $avatar_url);

            // Validate that it looks like an image URL
            if (filter_var($avatar_url, FILTER_VALIDATE_URL) &&
                preg_match('/\.(jpg|jpeg|png|gif|webp)(\?|$)/i', $avatar_url)) {
                return $avatar_url;
            }
        }
    }

    return false;
}

/**
 * Fetch Instagram avatar using embed method
 */
function fetchInstagramAvatarEmbed($username) {
    // Try Instagram's oembed endpoint
    $oembed_url = "https://api.instagram.com/oembed/?url=https://www.instagram.com/$username/";

    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (compatible; InstagramBot/1.0)',
                'Accept: application/json'
            ],
            'timeout' => 10
        ]
    ]);

    $response = @file_get_contents($oembed_url, false, $context);
    if ($response) {
        $data = json_decode($response, true);
        if (isset($data['thumbnail_url'])) {
            return $data['thumbnail_url'];
        }
    }

    return false;
}

/**
 * Fetch Instagram avatar using third-party services
 */
function fetchInstagramAvatarThirdParty($username) {
    // Use reliable third-party services that provide Instagram avatars
    $services = [
        "https://unavatar.io/instagram/$username",
        "https://unavatar.io/$username", // Generic service
        "https://avatars.githubusercontent.com/$username", // GitHub often has same username
    ];

    foreach ($services as $service_url) {
        // Check if the service returns a valid image
        $headers = @get_headers($service_url, 1);
        if ($headers && strpos($headers[0], '200') !== false) {
            $content_type = isset($headers['Content-Type']) ? $headers['Content-Type'] : '';
            if (is_array($content_type)) {
                $content_type = $content_type[0];
            }

            if (strpos($content_type, 'image/') === 0) {
                return $service_url;
            }
        }
    }

    return false;
}

/**
 * Fetch Twitter avatar using API method
 */
function fetchTwitterAvatarAPI($username) {
    // Twitter's public endpoints (limited access)
    $endpoints = [
        "https://api.twitter.com/1.1/users/show.json?screen_name=$username",
        "https://twitter.com/i/api/1.1/users/show.json?screen_name=$username"
    ];

    foreach ($endpoints as $url) {
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: Mozilla/5.0 (compatible; TwitterBot/1.0)',
                    'Accept: application/json'
                ],
                'timeout' => 10
            ]
        ]);

        $response = @file_get_contents($url, false, $context);
        if ($response) {
            $data = json_decode($response, true);
            if (isset($data['profile_image_url_https'])) {
                // Get higher resolution version
                $avatar_url = str_replace('_normal', '_400x400', $data['profile_image_url_https']);
                return $avatar_url;
            }
        }
    }

    return false;
}

/**
 * Fetch Twitter avatar using web scraping
 */
function fetchTwitterAvatarWeb($username) {
    $url = "https://twitter.com/$username";

    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            ],
            'timeout' => 10
        ]
    ]);

    $html = @file_get_contents($url, false, $context);

    if (!$html) {
        return false;
    }

    // Look for profile image in various patterns
    $patterns = [
        '/"profile_image_url_https":"([^"]+)"/',
        '/property="og:image" content="([^"]+)"/',
        '/name="twitter:image" content="([^"]+)"/',
        '/<img[^>]+data-testid="[^"]*avatar[^"]*"[^>]+src="([^"]+)"/',
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $html, $matches)) {
            $avatar_url = $matches[1];

            // Decode JSON escaped characters
            $avatar_url = str_replace('\/', '/', $avatar_url);
            $avatar_url = str_replace('\\u0026', '&', $avatar_url);

            // Get higher resolution version
            $avatar_url = str_replace('_normal', '_400x400', $avatar_url);

            // Validate that it looks like an image URL
            if (filter_var($avatar_url, FILTER_VALIDATE_URL) &&
                preg_match('/\.(jpg|jpeg|png|gif|webp)(\?|$)/i', $avatar_url)) {
                return $avatar_url;
            }
        }
    }

    return false;
}

/**
 * Fetch Twitter avatar using third-party services
 */
function fetchTwitterAvatarThirdParty($username) {
    // Use reliable third-party services that provide Twitter avatars
    $services = [
        "https://unavatar.io/twitter/$username",
        "https://avatars.githubusercontent.com/$username", // GitHub often has same username
        "https://www.gravatar.com/avatar/" . md5(strtolower($username)) . "?s=200&d=404"
    ];

    foreach ($services as $service_url) {
        // Check if the service returns a valid image
        $headers = @get_headers($service_url, 1);
        if ($headers && strpos($headers[0], '200') !== false) {
            $content_type = isset($headers['Content-Type']) ? $headers['Content-Type'] : '';
            if (is_array($content_type)) {
                $content_type = $content_type[0];
            }

            if (strpos($content_type, 'image/') === 0) {
                return $service_url;
            }
        }
    }

    return false;
}

/**
 * Fetch Instagram avatar using web scraping (legacy method)
 */
function fetchInstagramAvatarMethod($url, $username, $method) {
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language: en-US,en;q=0.5',
                'Accept-Encoding: gzip, deflate',
                'Connection: keep-alive',
            ],
            'timeout' => 10
        ]
    ]);

    $html = @file_get_contents($url, false, $context);

    if (!$html) {
        return false;
    }

    // Look for profile image in meta tags
    $patterns = [
        '/<meta property="og:image" content="([^"]+)"/',
        '/<meta name="twitter:image" content="([^"]+)"/',
        '/<img[^>]+class="[^"]*profile[^"]*"[^>]+src="([^"]+)"/',
        '/<img[^>]+src="([^"]+)"[^>]+class="[^"]*profile[^"]*"/',
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $html, $matches)) {
            $avatar_url = $matches[1];

            // Validate that it looks like an image URL
            if (filter_var($avatar_url, FILTER_VALIDATE_URL) &&
                preg_match('/\.(jpg|jpeg|png|gif|webp)(\?|$)/i', $avatar_url)) {
                return $avatar_url;
            }
        }
    }

    return false;
}

/**
 * Fetch Twitter avatar using web scraping
 */
function fetchTwitterAvatarMethod($url, $username) {
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            ],
            'timeout' => 10
        ]
    ]);

    $html = @file_get_contents($url, false, $context);

    if (!$html) {
        return false;
    }

    // Look for profile image in various places
    $patterns = [
        '/<img[^>]+data-testid="[^"]*avatar[^"]*"[^>]+src="([^"]+)"/',
        '/<img[^>]+src="([^"]+)"[^>]+data-testid="[^"]*avatar[^"]*"/',
        '/<meta property="og:image" content="([^"]+)"/',
        '/<meta name="twitter:image" content="([^"]+)"/',
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $html, $matches)) {
            $avatar_url = $matches[1];

            // Validate that it looks like an image URL
            if (filter_var($avatar_url, FILTER_VALIDATE_URL) &&
                preg_match('/\.(jpg|jpeg|png|gif|webp)(\?|$)/i', $avatar_url)) {
                return $avatar_url;
            }
        }
    }

    return false;
}

/**
 * Generate a default avatar based on username and platform
 */
function getDefaultAvatar($username, $platform = 'instagram') {
    // Create a deterministic color based on username
    $hash = md5($username);
    $hue = hexdec(substr($hash, 0, 2)) / 255 * 360;

    // Platform-specific styling
    $colors = [
        'instagram' => [
            'bg' => "hsl($hue, 70%, 50%)",
            'text' => '#ffffff'
        ],
        'twitter' => [
            'bg' => "hsl($hue, 60%, 45%)",
            'text' => '#ffffff'
        ]
    ];

    $color = $colors[$platform] ?? $colors['instagram'];

    // Get initials
    $initials = strtoupper(substr($username, 0, 2));

    // Generate SVG avatar
    $svg = '<?xml version="1.0" encoding="UTF-8"?>
    <svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
        <circle cx="75" cy="75" r="75" fill="' . $color['bg'] . '"/>
        <text x="75" y="85" font-family="Arial, sans-serif" font-size="48" font-weight="bold"
              text-anchor="middle" fill="' . $color['text'] . '">' . $initials . '</text>
    </svg>';

    // Convert to data URL
    return 'data:image/svg+xml;base64,' . base64_encode($svg);
}

/**
 * Update initiative avatar in database
 */
function updateInitiativeAvatar($db, $initiative_id, $avatar_url, $source) {
    $stmt = $db->prepare("UPDATE initiatives SET avatar_url = ?, avatar_source = ? WHERE id = ?");
    $stmt->bind_param("ssi", $avatar_url, $source, $initiative_id);

    if ($stmt->execute()) {
        $stmt->close();
        return true;
    } else {
        $stmt->close();
        return false;
    }
}

/**
 * Fetch and update avatar for an initiative
 */
function fetchAndUpdateAvatar($db, $initiative_id, $instagram_user = null, $twitter_user = null) {
    $results = [];

    // Try Instagram first if available
    if ($instagram_user) {
        $instagram_result = getInstagramAvatar($instagram_user);
        $results['instagram'] = $instagram_result;

        if ($instagram_result['success']) {
            if (updateInitiativeAvatar($db, $initiative_id, $instagram_result['avatar_url'], 'instagram')) {
                return [
                    'success' => true,
                    'avatar_url' => $instagram_result['avatar_url'],
                    'source' => 'instagram',
                    'method' => $instagram_result['method']
                ];
            }
        }
    }

    // Try Twitter if Instagram failed and Twitter is available
    if ($twitter_user) {
        $twitter_result = getTwitterAvatar($twitter_user);
        $results['twitter'] = $twitter_result;

        if ($twitter_result['success']) {
            if (updateInitiativeAvatar($db, $initiative_id, $twitter_result['avatar_url'], 'twitter')) {
                return [
                    'success' => true,
                    'avatar_url' => $twitter_result['avatar_url'],
                    'source' => 'twitter',
                    'method' => $twitter_result['method']
                ];
            }
        }
    }

    // Use fallback avatar
    $fallback_user = $instagram_user ?: $twitter_user ?: 'unknown';
    $fallback_source = $instagram_user ? 'instagram' : 'twitter';
    $fallback_avatar = getDefaultAvatar($fallback_user, $fallback_source);

    if (updateInitiativeAvatar($db, $initiative_id, $fallback_avatar, $fallback_source)) {
        return [
            'success' => false,
            'avatar_url' => $fallback_avatar,
            'source' => $fallback_source,
            'method' => 'fallback',
            'attempts' => $results
        ];
    }

    return [
        'success' => false,
        'error' => 'Failed to update avatar in database',
        'attempts' => $results
    ];
}

/**
 * Get avatar URL for display (with fallback)
 */
function getAvatarForDisplay($initiative) {
    // If we have a stored avatar, use it
    if (!empty($initiative['avatar_url'])) {
        return $initiative['avatar_url'];
    }

    // Generate fallback based on available social media
    $username = $initiative['instagram_user'] ?? $initiative['twitter_user'] ?? $initiative['name'];
    $platform = !empty($initiative['instagram_user']) ? 'instagram' : 'twitter';

    return getDefaultAvatar($username, $platform);
}
?>
