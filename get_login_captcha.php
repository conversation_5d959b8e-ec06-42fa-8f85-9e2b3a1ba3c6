<?php
session_start();

// Security headers
header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// Rate limiting for CAPTCHA generation
$client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
$client_fingerprint = md5($client_ip . $user_agent);

// Initialize session arrays if they don't exist
if (!isset($_SESSION['captcha_generation_attempts'])) {
    $_SESSION['captcha_generation_attempts'] = [];
}

// Get current attempts for this IP/fingerprint
$captcha_gen_attempts = $_SESSION['captcha_generation_attempts'][$client_fingerprint] ?? ['count' => 0, 'last_attempt' => 0];
$current_time = time();

// Rate limiting: max 20 CAPTCHA generations per 5 minutes
if ($captcha_gen_attempts['count'] >= 20 && ($current_time - $captcha_gen_attempts['last_attempt']) < 300) {
    http_response_code(429);
    echo json_encode([
        'error' => 'Too many CAPTCHA requests. Please wait before requesting a new one.',
        'retry_after' => 300 - ($current_time - $captcha_gen_attempts['last_attempt'])
    ]);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Verify referrer to prevent CSRF
$allowed_referrers = [
    'https://abla.lat',
    'http://localhost',
    'http://127.0.0.1'
];

$referrer = $_SERVER['HTTP_REFERER'] ?? '';
$referrer_valid = false;

foreach ($allowed_referrers as $allowed) {
    if (strpos($referrer, $allowed) === 0) {
        $referrer_valid = true;
        break;
    }
}

if (!$referrer_valid && !empty($referrer)) {
    http_response_code(403);
    echo json_encode(['error' => 'Invalid referrer']);
    exit;
}

try {
    // Generate new CAPTCHA
    $chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $captcha = '';
    
    // Use cryptographically secure random
    for ($i = 0; $i < 6; $i++) {
        $captcha .= $chars[random_int(0, strlen($chars) - 1)];
    }
    
    // Store in session
    $_SESSION['login_captcha'] = $captcha;
    
    // Update generation attempts
    $captcha_gen_attempts['count']++;
    $captcha_gen_attempts['last_attempt'] = $current_time;
    $_SESSION['captcha_generation_attempts'][$client_fingerprint] = $captcha_gen_attempts;
    
    // Clean up old session data
    $cleanup_time = $current_time - 1800; // 30 minutes
    foreach ($_SESSION['captcha_generation_attempts'] as $fingerprint => $data) {
        if ($data['last_attempt'] < $cleanup_time) {
            unset($_SESSION['captcha_generation_attempts'][$fingerprint]);
        }
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'captcha' => $captcha,
        'timestamp' => $current_time
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Failed to generate CAPTCHA',
        'message' => 'Please refresh the page and try again'
    ]);
}
?>
